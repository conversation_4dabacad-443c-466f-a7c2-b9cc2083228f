# Especificações Técnicas - Sistema de Emails Refatorado

## Estrutura de Dados

### Email no Redis
```javascript
{
  // Dados básicos do email
  id: "email_12345_*************",
  to: "<EMAIL>",
  cc: "<EMAIL>",
  bcc: "<EMAIL>",
  from: "<EMAIL>",
  fromName: "QiPlus",
  subject: "Assunto do Email",
  html: "<html>...</html>",
  text: "Versão texto",
  
  // Configurações do provedor
  smtp: "qiplus_smtp", // ou "mailgun", "resend", "other_smtp"
  integrationId: "integration_123",
  
  // Agendamento e controle
  scheduled_date: "2023-12-31T23:59:59.000Z",
  _scheduled_timestamp: *************,
  _scheduled_iso: "2023-12-31T23:59:59.000Z",
  
  // Controle de tentativas
  attempts: 0,
  max_attempts: 3,
  last_attempt: null,
  next_retry: null,
  
  // Metadados
  accountId: "account_123",
  owner: "user_456",
  context: { /* contexto adicional */ },
  emailVars: { /* variáveis do template */ },
  
  // Rastreamento
  trackId: null,
  tags: ["campaign_1", "newsletter"],
  triggerId: "trigger_789",
  
  // Timestamps de controle
  created_at: "2023-12-31T12:00:00.000Z",
  prepared_at: "2023-12-31T12:00:01.000Z",
  first_attempt_at: null,
  sent_at: null,
  
  // Anexos (se houver)
  attachments: [
    {
      filename: "document.pdf",
      content: "base64_content",
      contentType: "application/pdf"
    }
  ]
}
```

### Chaves Redis Utilizadas

#### Lista Principal de Emails Agendados
- **Chave**: `email:scheduled_emails`
- **Tipo**: Sorted Set (ZSET)
- **Score**: Timestamp de agendamento
- **Valor**: Chave do email individual

#### Email Individual
- **Chave**: `email:message:{emailId}`
- **Tipo**: Hash
- **Conteúdo**: Dados completos do email

#### Dead Letter Queue
- **Chave**: `email:dead_letter_queue`
- **Tipo**: Sorted Set (ZSET)
- **Score**: Timestamp da falha final
- **Valor**: Chave do email com falha

#### Métricas e Controle
- **Chave**: `email:metrics:daily:{YYYY-MM-DD}`
- **Tipo**: Hash
- **Campos**: `prepared`, `sent`, `failed`, `retried`

## Interface de Provedores

### Estrutura Base
```javascript
class EmailProvider {
  constructor(config) {
    this.config = config;
    this.name = 'base';
  }
  
  async sendEmail(emailData) {
    throw new Error('sendEmail must be implemented');
  }
  
  async validateConfig() {
    throw new Error('validateConfig must be implemented');
  }
  
  isTemporaryError(error) {
    // Implementação base para detectar erros temporários
    return false;
  }
  
  formatError(error) {
    return {
      message: error.message,
      code: error.code || 'UNKNOWN',
      temporary: this.isTemporaryError(error)
    };
  }
}
```

### Implementações Específicas

#### MailgunProvider
```javascript
class MailgunProvider extends EmailProvider {
  constructor(config) {
    super(config);
    this.name = 'mailgun';
    this.client = mailgun({
      apiKey: config.apiKey,
      domain: config.domain
    });
  }
  
  async sendEmail(emailData) {
    // Implementação específica do Mailgun
  }
  
  isTemporaryError(error) {
    // Códigos de erro temporário do Mailgun
    const temporaryCodes = [429, 500, 502, 503, 504];
    return temporaryCodes.includes(error.status);
  }
}
```

#### ResendProvider
```javascript
class ResendProvider extends EmailProvider {
  constructor(config) {
    super(config);
    this.name = 'resend';
    this.client = new Resend(config.apiKey);
  }
  
  async sendEmail(emailData) {
    // Implementação específica do Resend
  }
  
  isTemporaryError(error) {
    // Códigos de erro temporário do Resend
    return error.name === 'RateLimitError' || 
           error.name === 'InternalServerError';
  }
}
```

## Sistema de Retry

### Configuração de Backoff Exponencial
```javascript
const RETRY_CONFIG = {
  maxAttempts: 3,
  baseDelay: 60000, // 1 minuto
  maxDelay: 3600000, // 1 hora
  backoffMultiplier: 2,
  jitter: true
};

function calculateNextRetry(attempt) {
  const delay = Math.min(
    RETRY_CONFIG.baseDelay * Math.pow(RETRY_CONFIG.backoffMultiplier, attempt),
    RETRY_CONFIG.maxDelay
  );
  
  // Adicionar jitter para evitar thundering herd
  const jitter = RETRY_CONFIG.jitter ? Math.random() * 0.1 * delay : 0;
  
  return Date.now() + delay + jitter;
}
```

### Lógica de Retry
1. **Primeira tentativa**: Imediata
2. **Segunda tentativa**: 1 minuto após falha
3. **Terceira tentativa**: 2 minutos após segunda falha
4. **Falha final**: Move para Dead Letter Queue

## Logs Estruturados

### Formato de Log
```javascript
const logEntry = {
  timestamp: "2023-12-31T12:00:00.000Z",
  level: "INFO", // DEBUG, INFO, WARN, ERROR
  component: "EmailSend",
  operation: "sendEmail",
  emailId: "email_12345_*************",
  accountId: "account_123",
  provider: "mailgun",
  attempt: 1,
  duration: 1250, // ms
  success: true,
  error: null,
  metadata: {
    to: "<EMAIL>",
    subject: "Assunto do Email",
    trackId: "track_789"
  }
};
```

### Níveis de Log
- **DEBUG**: Detalhes internos de processamento
- **INFO**: Operações normais (envio, retry, etc.)
- **WARN**: Situações que requerem atenção (retry, configuração)
- **ERROR**: Falhas que impedem o funcionamento

## Métricas e Monitoramento

### Métricas Coletadas
```javascript
const metrics = {
  // Contadores diários
  emails_prepared: 0,
  emails_sent: 0,
  emails_failed: 0,
  emails_retried: 0,
  
  // Por provedor
  mailgun_sent: 0,
  mailgun_failed: 0,
  resend_sent: 0,
  resend_failed: 0,
  
  // Tempos de processamento
  avg_send_time: 0,
  max_send_time: 0,
  
  // Queue status
  queue_size: 0,
  dlq_size: 0,
  
  // Taxa de sucesso
  success_rate: 0.95
};
```

### Alertas Configurados
1. **Taxa de sucesso < 90%**: Alerta crítico
2. **DLQ com > 100 emails**: Alerta de atenção
3. **Queue com > 1000 emails**: Alerta de performance
4. **Tempo médio > 5 segundos**: Alerta de latência

## Configuração de Ambiente

### Variáveis Redis
```bash
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=senhaMuitoBo@0192
REDIS_DATABASE=0
REDIS_TIMEOUT=10000
```

### Configuração de Provedores
```javascript
const emailConfig = {
  defaultProvider: 'mailgun',
  providers: {
    mailgun: {
      apiKey: functions.config().mailgun.apikey,
      domain: 'mail.qiplus.com.br',
      enabled: true
    },
    resend: {
      apiKey: functions.config().resend.apikey,
      domain: 'qiplus.com.br',
      enabled: true
    },
    smtp: {
      host: 'smtp.zoho.com',
      port: 465,
      secure: true,
      auth: {
        user: functions.config().smtp.user,
        pass: functions.config().smtp.pass
      },
      enabled: false
    }
  }
};
```

## Performance e Escalabilidade

### Processamento em Lote
- **Tamanho do lote**: 50 emails por execução
- **Timeout por email**: 30 segundos
- **Timeout total**: 10 minutos

### Otimizações
1. **Conexão Redis persistente**: Reutilizar conexões
2. **Pool de conexões**: Para provedores HTTP
3. **Processamento paralelo**: Até 5 emails simultâneos
4. **Cache de configuração**: Evitar consultas repetidas

### Limites Operacionais
- **Máximo 1000 emails na queue**: Prevenir sobrecarga
- **Máximo 100 emails na DLQ**: Alertar para investigação
- **Retenção de logs**: 30 dias
- **Retenção de métricas**: 90 dias

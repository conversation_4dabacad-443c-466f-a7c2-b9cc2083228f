# Guia de Configuração - Sistema de Emails Refatorado

## Configurações de Ambiente

### Variáveis Redis (Firebase Config)

```bash
# Configurar via Firebase CLI
firebase functions:config:set redis.host="localhost"
firebase functions:config:set redis.port="6379"
firebase functions:config:set redis.password="senhaMuitoBo@0192"
firebase functions:config:set redis.database="0"
```

### Configurações de Provedores de Email

```bash
# Mailgun
firebase functions:config:set mailgun.apikey="sua-api-key-mailgun"

# Resend
firebase functions:config:set resend.apikey="sua-api-key-resend"

# SMTP (Zoho)
firebase functions:config:set smtp.user="<EMAIL>"
firebase functions:config:set smtp.pass="sua-senha-smtp"
```

### Verificar Configurações

```bash
# Ver todas as configurações
firebase functions:config:get

# Ver configurações específicas
firebase functions:config:get redis
firebase functions:config:get mailgun
firebase functions:config:get resend
```

## Configurações do Sistema

### 1. Configuração de Provedores

Editar `functions/emailSystem/index.js`:

```javascript
const EMAIL_CONFIG = {
  defaultProvider: 'mailgun', // ou 'resend', 'smtp'
  
  providers: {
    mailgun: {
      enabled: true,
      priority: 1, // Prioridade (menor = maior prioridade)
      domain: 'mail.qiplus.com.br'
    },
    resend: {
      enabled: true,
      priority: 2,
      domain: 'qiplus.com.br'
    },
    smtp: {
      enabled: false, // Desabilitado por padrão
      priority: 3,
      host: 'smtp.zoho.com',
      port: 465,
      secure: true
    }
  }
};
```

### 2. Configuração de Retry

Editar `functions/emailSystem/retryManager.js`:

```javascript
const RETRY_CONFIG = {
  strategies: {
    rate_limit: {
      maxAttempts: 5,
      baseDelay: 300000, // 5 minutos
      maxDelay: 3600000, // 1 hora
    },
    network_error: {
      maxAttempts: 3,
      baseDelay: 60000, // 1 minuto
      maxDelay: 600000, // 10 minutos
    },
    default: {
      maxAttempts: 3,
      baseDelay: 60000,
      maxDelay: 3600000
    }
  }
};
```

### 3. Configuração de Logs

Editar `functions/emailSystem/logger.js`:

```javascript
const LOGGER_CONFIG = {
  // Nível mínimo de log em produção
  minLevel: process.env.NODE_ENV === 'production' ? LOG_LEVELS.INFO : LOG_LEVELS.DEBUG,
  
  // Configurações de métricas
  metrics: {
    enabled: true,
    retentionDays: 90
  },
  
  // Configurações de alertas
  alerts: {
    enabled: true,
    errorThreshold: 10, // 10 erros por minuto
    slowRequestThreshold: 5 // 5 requests lentos por minuto
  }
};
```

### 4. Configuração de Migração

Editar `functions/emailSystem/migration.js`:

```javascript
const MIGRATION_CONFIG = {
  migration: {
    enabled: true,        // Habilitar migração
    forceNewSystem: true, // Forçar uso do novo sistema
    allowFallback: false, // Permitir fallback para sistema antigo
    logLegacyCalls: true  // Logar chamadas do sistema antigo
  }
};
```

## Configurações de Performance

### 1. Configuração de Batch Size

```javascript
// Em functions/emailPrepare/index.js
const EMAIL_PREPARE_CONFIG = {
  batchSize: 100, // Emails por execução
  redisTimeout: 30000 // 30 segundos
};

// Em functions/emailSend/index.js
const EMAIL_SEND_CONFIG = {
  batchSize: 50, // Emails por execução
  sendTimeout: 30000 // 30 segundos por email
};
```

### 2. Configuração de Timeouts

```javascript
// Em functions/emailSystem/cronIntegration.js
const CRON_INTEGRATION_CONFIG = {
  timeouts: {
    prepare: 120000, // 2 minutos
    send: 300000,    // 5 minutos
    cleanup: 60000   // 1 minuto
  }
};
```

### 3. Configuração de Limpeza

```javascript
// Em functions/emailSystem/retryManager.js
const RETRY_CONFIG = {
  dlq: {
    maxAge: 30, // dias
    alertThreshold: 100, // alertar quando DLQ > 100 emails
    cleanupInterval: 24 * 60 * 60 * 1000 // 24 horas
  }
};
```

## Monitoramento e Alertas

### 1. Métricas Disponíveis

```javascript
// Estatísticas do sistema
const { getEmailSystemStats } = require("./emailSystem/cronIntegration");
const stats = await getEmailSystemStats();

console.log(stats);
// {
//   prepare: { queueSize: 10, readyCount: 5 },
//   send: { sent: 100, failed: 2, retried: 5 },
//   retry: { total: 50, successful: 45, failed: 5 }
// }
```

### 2. Logs Estruturados

```javascript
// Logs são automaticamente estruturados
// Exemplo de saída:
{
  "timestamp": "2023-12-31T12:00:00.000Z",
  "level": "INFO",
  "component": "EmailSend",
  "message": "Email sent successfully",
  "emailId": "email_123_456",
  "provider": "mailgun",
  "duration": 1250
}
```

### 3. Alertas Automáticos

O sistema gera alertas automáticos para:
- Taxa de erro > 90%
- Dead Letter Queue > 100 emails
- Fila principal > 1000 emails
- Tempo médio de envio > 5 segundos

## Configurações de Segurança

### 1. Sanitização de Logs

```javascript
// Campos sensíveis são automaticamente removidos dos logs
const sensitiveFields = ['password', 'apiKey', 'token', 'secret', 'auth'];
```

### 2. Validação de Emails

```javascript
// Validação automática de formato de email
const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
```

### 3. Timeouts de Segurança

```javascript
// Timeouts para prevenir operações infinitas
const timeouts = {
  sendEmail: 30000,    // 30 segundos
  validateConfig: 5000, // 5 segundos
  redisOperation: 10000 // 10 segundos
};
```

## Configurações de Desenvolvimento

### 1. Modo de Teste

```javascript
// Para executar em modo de teste
const result = await emailSend({
  batchSize: 1,
  simulateOnly: true // Não envia emails reais
});
```

### 2. Logs de Debug

```javascript
// Habilitar logs detalhados em desenvolvimento
process.env.NODE_ENV = 'development';
// Isso automaticamente define LOG_LEVEL para DEBUG
```

### 3. Testes Rápidos

```javascript
// Executar teste rápido do sistema
const { runQuickTest } = require("./emailSystem/tests/emailSystemTests");
const result = await runQuickTest();
```

## Configurações de Backup

### 1. Retenção de Dados

```javascript
// Configurações de retenção
const retention = {
  logs: 30,      // dias
  metrics: 90,   // dias
  dlq: 30,       // dias
  errorPatterns: 30 // dias
};
```

### 2. Backup de Configurações

```bash
# Backup das configurações do Firebase
firebase functions:config:get > config-backup.json

# Restaurar configurações
firebase functions:config:set --import config-backup.json
```

## Configurações de Escalabilidade

### 1. Limites Operacionais

```javascript
const limits = {
  maxEmailsInQueue: 1000,
  maxEmailsInDLQ: 100,
  maxConcurrentSends: 5,
  maxRetryAttempts: 3
};
```

### 2. Configuração de Memória

```javascript
// Em functions/index.js
exports.cron1Minutes = functions
  .runWith({ 
    memory: "4GB",           // Memória alocada
    timeoutSeconds: 540      // 9 minutos de timeout
  })
  .pubsub.schedule("every 1 minutes")
  .onRun(cronHandler);
```

### 3. Pool de Conexões

```javascript
// Configuração automática de pool de conexões Redis
const REDIS_CONFIG = {
  maxRetriesPerRequest: 3,
  enableReadyCheck: true,
  disableOfflineQueue: false
};
```

## Configurações Específicas por Ambiente

### Desenvolvimento

```javascript
const devConfig = {
  logLevel: 'DEBUG',
  batchSize: 10,
  simulateOnly: true,
  enableAllProviders: false
};
```

### Staging

```javascript
const stagingConfig = {
  logLevel: 'INFO',
  batchSize: 50,
  simulateOnly: false,
  enableAllProviders: true
};
```

### Produção

```javascript
const prodConfig = {
  logLevel: 'INFO',
  batchSize: 100,
  simulateOnly: false,
  enableAllProviders: true,
  enableAlerts: true
};
```

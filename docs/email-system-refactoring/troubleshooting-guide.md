# Guia de Troubleshooting - Sistema de Emails

## Problemas Comuns e Soluções

### 1. Emails Não Estão Sendo Enviados

#### Sintomas
- Emails ficam na fila sem serem processados
- Logs mostram "No emails ready for sending"

#### Diagnóstico
```javascript
// Verificar estatísticas do sistema
const { getEmailSystemStats } = require("./emailSystem/cronIntegration");
const stats = await getEmailSystemStats();
console.log(stats);
```

#### Possíveis Causas e Soluções

**A. Redis não está conectado**
```bash
# Verificar configuração Redis
firebase functions:config:get redis

# Testar conexão
const { runQuickTest } = require("./emailSystem/tests/emailSystemTests");
await runQuickTest();
```

**B. Emails não estão sendo preparados**
```javascript
// Verificar se emailPrepare está funcionando
const { emailPrepare } = require("./emailPrepare");
const result = await emailPrepare({ batchSize: 1 });
console.log(result);
```

**C. Data de agendamento incorreta**
```sql
-- Verificar emails no Firestore
SELECT scheduled_date, prepared, sent, error 
FROM emails 
WHERE scheduled_date <= NOW() 
AND prepared = false 
AND sent = false 
AND error = false
```

### 2. Emails Ficam na Dead Letter Queue

#### Sintomas
- Muitos emails na DLQ
- Alertas de DLQ threshold

#### Diagnóstico
```javascript
// Verificar emails na DLQ
const { getDLQManager } = require("./emailSystem/retryManager");
const dlqManager = getDLQManager();
const failedEmails = await dlqManager.getDeadLetterEmails({ limit: 10 });
console.log(failedEmails);
```

#### Soluções

**A. Reprocessar emails específicos**
```javascript
// Reprocessar email individual
await dlqManager.reprocessEmail(emailId);

// Reprocessar múltiplos emails
const failedEmails = await dlqManager.getDeadLetterEmails();
for (const email of failedEmails.slice(0, 10)) {
  await dlqManager.reprocessEmail(email.id);
}
```

**B. Analisar padrões de erro**
```javascript
// Verificar estatísticas de retry
const { getRetryManager } = require("./emailSystem/retryManager");
const retryManager = getRetryManager();
const stats = await retryManager.getRetryStats();
console.log(stats.errorPatterns);
```

### 3. Problemas de Performance

#### Sintomas
- Cron jobs demoram muito para executar
- Timeouts frequentes
- Alertas de slow requests

#### Diagnóstico
```javascript
// Verificar operações ativas
const { getPerformanceTracker } = require("./emailSystem/logger");
const tracker = getPerformanceTracker();
const activeOps = tracker.getActiveOperations();
console.log(activeOps);
```

#### Soluções

**A. Ajustar batch sizes**
```javascript
// Reduzir batch size em functions/emailPrepare/index.js
const EMAIL_PREPARE_CONFIG = {
  batchSize: 50 // Reduzir de 100 para 50
};

// Reduzir batch size em functions/emailSend/index.js
const EMAIL_SEND_CONFIG = {
  batchSize: 25 // Reduzir de 50 para 25
};
```

**B. Aumentar timeouts**
```javascript
// Em functions/emailSystem/cronIntegration.js
const CRON_INTEGRATION_CONFIG = {
  timeouts: {
    prepare: 180000, // Aumentar para 3 minutos
    send: 450000,    // Aumentar para 7.5 minutos
    cleanup: 90000   // Aumentar para 1.5 minutos
  }
};
```

### 4. Problemas de Provedor

#### Sintomas
- Falhas específicas de um provedor
- Rate limiting constante
- Erros de autenticação

#### Diagnóstico
```javascript
// Verificar status dos provedores
const { getEmailSystem } = require("./emailSystem");
const emailSystem = getEmailSystem();
const providerStats = emailSystem.getProviderStats();
console.log(providerStats);

// Testar configuração de provedor específico
const { validateProviderConfig } = require("./emailSystem");
const isValid = await validateProviderConfig('mailgun');
console.log('Mailgun config valid:', isValid);
```

#### Soluções

**A. Trocar provedor padrão**
```javascript
// Em functions/emailSystem/index.js
const EMAIL_CONFIG = {
  defaultProvider: 'resend', // Trocar de 'mailgun' para 'resend'
};
```

**B. Desabilitar provedor problemático**
```javascript
// Em functions/emailSystem/index.js
const EMAIL_CONFIG = {
  providers: {
    mailgun: {
      enabled: false, // Desabilitar temporariamente
    }
  }
};
```

**C. Ajustar configurações de retry para rate limiting**
```javascript
// Em functions/emailSystem/retryManager.js
const RETRY_CONFIG = {
  strategies: {
    rate_limit: {
      maxAttempts: 8,      // Aumentar tentativas
      baseDelay: 600000,   // Aumentar delay para 10 minutos
      maxDelay: 7200000,   // Aumentar max delay para 2 horas
    }
  }
};
```

### 5. Problemas de Migração

#### Sintomas
- Sistema antigo ainda sendo usado
- Inconsistências entre sistemas
- Erros de compatibilidade

#### Diagnóstico
```javascript
// Verificar estatísticas de migração
const { getMigrationStats } = require("./emailSystem/migration");
const migrationStats = await getMigrationStats();
console.log(migrationStats);
```

#### Soluções

**A. Forçar uso do novo sistema**
```javascript
// Em functions/emailSystem/migration.js
const MIGRATION_CONFIG = {
  migration: {
    enabled: true,
    forceNewSystem: true,
    allowFallback: false
  }
};
```

**B. Habilitar logs detalhados**
```javascript
// Em functions/emailSystem/migration.js
const MIGRATION_CONFIG = {
  migration: {
    logLegacyCalls: true
  }
};
```

### 6. Problemas de Redis

#### Sintomas
- Erros de conexão Redis
- Timeouts de Redis
- Dados perdidos

#### Diagnóstico
```bash
# Verificar configuração Redis
firebase functions:config:get redis

# Testar conexão manual
redis-cli -h localhost -p 6379 -a "senhaMuitoBo@0192" ping
```

#### Soluções

**A. Verificar configurações de rede**
```javascript
// Em functions/utils/redisClient.js
const REDIS_CONFIG = {
  socket: {
    connectTimeout: 20000,  // Aumentar timeout
    reconnectStrategy: (retries) => Math.min(retries * 100, 3000)
  }
};
```

**B. Limpar dados corrompidos**
```javascript
// Limpar chaves específicas
const { getRedisClient } = require("./utils/redisClient");
const client = await getRedisClient();

// Limpar fila de emails
await client.del('email:scheduled_emails');

// Limpar DLQ
await client.del('email:dead_letter_queue');
```

## Comandos de Diagnóstico

### 1. Verificação Rápida do Sistema

```javascript
// Executar teste completo
const { runEmailSystemTests } = require("./emailSystem/tests/emailSystemTests");
const testResults = await runEmailSystemTests();
console.log(testResults.summary);
```

### 2. Verificação de Configuração

```bash
# Verificar todas as configurações
firebase functions:config:get

# Verificar logs recentes
firebase functions:log --only functions:cron1Minutes --limit 50
```

### 3. Verificação de Métricas

```javascript
// Métricas detalhadas
const { getEmailLogger } = require("./emailSystem/logger");
const logger = getEmailLogger();
const metrics = await logger.getAggregatedMetrics({
  startDate: '2023-12-30',
  endDate: '2023-12-31'
});
console.log(metrics);
```

## Logs de Debug

### 1. Habilitar Logs Detalhados

```javascript
// Temporariamente em desenvolvimento
process.env.NODE_ENV = 'development';

// Ou ajustar nível de log
const LOGGER_CONFIG = {
  minLevel: LOG_LEVELS.DEBUG
};
```

### 2. Interpretar Logs

```json
{
  "timestamp": "2023-12-31T12:00:00.000Z",
  "level": "ERROR",
  "component": "EmailSend",
  "message": "Failed to send email",
  "emailId": "email_123_456",
  "error": "Rate limit exceeded",
  "provider": "mailgun",
  "attempts": 2
}
```

**Interpretação:**
- Email com ID `email_123_456` falhou no envio
- Erro de rate limit no Mailgun
- Já foram feitas 2 tentativas
- Será reagendado para retry

### 3. Filtrar Logs por Componente

```bash
# Logs do EmailPrepare
firebase functions:log --only functions:cron1Minutes | grep "EmailPrepare"

# Logs do EmailSend
firebase functions:log --only functions:cron1Minutes | grep "EmailSend"

# Logs de erro
firebase functions:log --only functions:cron1Minutes | grep "ERROR"
```

## Recuperação de Emergência

### 1. Fallback para Sistema Antigo

```javascript
// Em functions/emailSystem/migration.js
const MIGRATION_CONFIG = {
  migration: {
    enabled: false, // Desabilitar migração
    allowFallback: true
  }
};
```

### 2. Limpeza Completa do Sistema

```javascript
// CUIDADO: Isso remove todos os emails da fila
const { getRedisClient } = require("./utils/redisClient");
const client = await getRedisClient();

// Remover todas as chaves de email
const keys = await client.keys('email:*');
for (const key of keys) {
  await client.del(key);
}
```

### 3. Reprocessar Emails Manualmente

```javascript
// Buscar emails não enviados no Firestore
const { FirestoreRef, COLLECTIONS } = require("./init");

const unsentEmails = await FirestoreRef
  .collection(COLLECTIONS.MAIL_COLLECTION_NAME)
  .where("sent", "==", false)
  .where("error", "==", false)
  .limit(100)
  .get();

// Marcar para reprocessamento
const batch = FirestoreRef.batch();
unsentEmails.forEach(doc => {
  batch.update(doc.ref, { prepared: false });
});
await batch.commit();
```

## Contatos de Suporte

Para problemas não resolvidos por este guia:

1. **Logs Detalhados**: Sempre incluir logs relevantes
2. **Configuração**: Incluir configurações anonimizadas
3. **Reprodução**: Passos para reproduzir o problema
4. **Impacto**: Quantos emails/usuários afetados

### Informações Úteis para Suporte

```javascript
// Coletar informações do sistema
const systemInfo = {
  nodeVersion: process.version,
  environment: process.env.NODE_ENV,
  timestamp: new Date().toISOString(),
  stats: await getEmailSystemStats(),
  config: {
    // Configurações anonimizadas
    redisConnected: !!(await getRedisClient()),
    providersEnabled: Object.keys(EMAIL_CONFIG.providers).filter(p => 
      EMAIL_CONFIG.providers[p].enabled
    )
  }
};

console.log(JSON.stringify(systemInfo, null, 2));
```

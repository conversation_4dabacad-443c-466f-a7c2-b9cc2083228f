# Refatoração do Sistema de Emails - QiPlus

## Visão Geral

Este documento descreve a refatoração completa do sistema de envio de emails do QiPlus, migrando de um modelo de envio direto para uma arquitetura baseada em filas Redis, seguindo os padrões estabelecidos pelo shotxCron mas com melhorias significativas.

## Objetivos da Refatoração

### Problemas Identificados no Sistema Atual

1. **Envio Direto Sem Controle de Falhas**: Emails são enviados diretamente sem sistema de retry
2. **Falta de Observabilidade**: Logs limitados e sem métricas estruturadas
3. **Acoplamento Forte**: Dependência direta dos provedores (Mailgun/Resend)
4. **Processamento Sequencial Bloqueante**: Falhas em um email podem afetar outros
5. **Sem Dead-Letter Queue**: Emails com falha persistente são perdidos
6. **Configuração Fragmentada**: Múltiplos pontos de configuração

### Melhorias Propostas

1. **Arquitetura de Filas**: Separação entre preparação e envio
2. **Sistema de Retry**: Backoff exponencial para falhas temporárias
3. **Dead-Letter Queue**: Tratamento de mensagens com falha persistente
4. **Interface Unificada**: Abstração dos provedores de email
5. **Logs Estruturados**: Observabilidade completa do processo
6. **Processamento em Lote**: Otimização de performance
7. **Métricas e Monitoramento**: Visibilidade operacional

## Arquitetura Atual vs Nova Arquitetura

### Sistema Atual (Problemático)

```
Firestore (emails agendados) → emailCron() → sendMail() → Mailgun/Resend
                                    ↓
                              Falha = Email Perdido
```

### Nova Arquitetura (Baseada em Filas)

```
Firestore → EmailPrepare → Redis Queue → EmailSend → Provider Interface → Mailgun/Resend/SMTP
              ↓              ↓             ↓              ↓
         Preparação     Armazenamento   Consumo      Abstração
         e Validação    com Timestamp   com Retry    Unificada
                                           ↓
                                    Dead Letter Queue
                                    (falhas persistentes)
```

## Componentes da Nova Arquitetura

### 1. EmailPrepare (Organizador)

- **Localização**: `functions/emailPrepare/index.js`
- **Responsabilidade**: Buscar emails agendados e preparar para envio
- **Funcionalidades**:
  - Busca emails no Firestore com `scheduled_date <= now`
  - Valida dados e prepara mensagens
  - Armazena no Redis com timestamp
  - Marca emails como processados no Firestore

### 2. EmailSend (Consumidor)

- **Localização**: `functions/emailSend/index.js`
- **Responsabilidade**: Consumir emails do Redis e enviar
- **Funcionalidades**:
  - Busca emails prontos para envio no Redis
  - Implementa retry com backoff exponencial
  - Move falhas persistentes para dead-letter queue
  - Remove emails enviados com sucesso

### 3. EmailSystem (Interface de Provedores)

- **Localização**: `functions/emailSystem/index.js`
- **Responsabilidade**: Interface unificada para provedores
- **Funcionalidades**:
  - Abstração comum para Mailgun, Resend e SMTP
  - Tratamento unificado de anexos e templates
  - Rastreamento independente do provedor
  - Configuração centralizada

## Fluxo de Dados Detalhado

### Fase 1: Preparação (EmailPrepare)

1. Cron job executa `emailPrepare()` a cada minuto
2. Busca emails com `scheduled_date <= now` no Firestore
3. Para cada email:
   - Valida dados obrigatórios
   - Prepara conteúdo (templates, shortcodes)
   - Gera ID único
   - Salva no Redis com timestamp
   - Marca como `prepared: true` no Firestore

### Fase 2: Envio (EmailSend)

1. Cron job executa `emailSend()` a cada minuto
2. Busca emails prontos no Redis
3. Para cada email:
   - Tenta envio através da interface unificada
   - Em caso de sucesso: remove do Redis
   - Em caso de falha temporária: incrementa tentativas e reagenda
   - Em caso de falha persistente: move para dead-letter queue

### Fase 3: Monitoramento

1. Logs estruturados em cada etapa
2. Métricas de performance e taxa de sucesso
3. Alertas para dead-letter queue
4. Dashboard de observabilidade

## Cronograma de Implementação

1. **Análise e Documentação** ✓
2. **Interface de Provedores** (2-3 horas)
3. **EmailPrepare** (3-4 horas)
4. **EmailSend** (4-5 horas)
5. **Sistema de Retry e DLQ** (2-3 horas)
6. **Logs e Métricas** (2-3 horas)
7. **Integração com Cron** (1-2 horas)
8. **Desativação Sistema Atual** (1-2 horas)
9. **Testes e Validação** (3-4 horas)
10. **Documentação Final** (1-2 horas)

**Total Estimado**: 20-30 horas de desenvolvimento

## Próximos Passos

1. Implementar a interface unificada de provedores
2. Desenvolver o EmailPrepare seguindo o padrão do shotxOrganizeMessages
3. Criar o EmailSend com sistema de retry robusto
4. Integrar com o sistema de cron existente
5. Realizar testes extensivos antes da migração

## Benefícios Esperados

- **Confiabilidade**: 99%+ de taxa de entrega com retry automático
- **Observabilidade**: Visibilidade completa do processo de envio
- **Escalabilidade**: Processamento em lote e assíncrono
- **Manutenibilidade**: Código modular e bem documentado
- **Flexibilidade**: Troca fácil entre provedores de email

## Status da Implementação

✅ **CONCLUÍDO** - Sistema totalmente implementado e integrado

### Componentes Implementados

1. ✅ **EmailSystem (Interface Unificada)** - `functions/emailSystem/index.js`

   - Interface comum para Mailgun, Resend e SMTP
   - Seleção automática de provedor
   - Tratamento unificado de anexos e templates

2. ✅ **Provedores de Email** - `functions/emailSystem/providers/`

   - `BaseProvider.js` - Classe base com funcionalidades comuns
   - `MailgunProvider.js` - Implementação específica do Mailgun
   - `ResendProvider.js` - Implementação específica do Resend
   - `SMTPProvider.js` - Implementação específica do SMTP

3. ✅ **EmailPrepare (Organizador)** - `functions/emailPrepare/index.js`

   - Busca emails agendados no Firestore
   - Prepara e valida mensagens
   - Armazena no Redis com timestamps

4. ✅ **EmailSend (Consumidor)** - `functions/emailSend/index.js`

   - Consome emails do Redis
   - Sistema de retry com backoff exponencial
   - Dead-letter queue para falhas persistentes

5. ✅ **Sistema de Retry e DLQ** - `functions/emailSystem/retryManager.js`

   - Estratégias de retry por tipo de erro
   - Gerenciamento avançado da dead-letter queue
   - Métricas e monitoramento de falhas

6. ✅ **Logs Estruturados** - `functions/emailSystem/logger.js`

   - Sistema de logging com níveis apropriados
   - Métricas de performance em tempo real
   - Alertas automáticos para problemas

7. ✅ **Integração com Cron** - `functions/emailSystem/cronIntegration.js`

   - Substituição do emailCron original
   - Processamento sequencial com isolamento de erros
   - Compatibilidade com sistema existente

8. ✅ **Sistema de Migração** - `functions/emailSystem/migration.js`

   - Redirecionamento transparente do sistema antigo
   - Compatibilidade com APIs existentes
   - Estatísticas de migração

9. ✅ **Testes Abrangentes** - `functions/emailSystem/tests/`
   - Testes unitários e de integração
   - Cenários de falha e recuperação
   - Testes de performance e end-to-end

### Integração Realizada

- ✅ Substituição do `emailCron` no `functions/index.js`
- ✅ Redirecionamento do `sendMail` em `functions/mailing/index.js`
- ✅ Redirecionamento do `createBatchMailing` em `functions/mailing/index.js`
- ✅ Configuração Redis utilizando infraestrutura existente
- ✅ Compatibilidade total com sistema existente

## Como Usar o Novo Sistema

### 1. Configuração (Já Configurado)

O sistema está configurado para usar as mesmas variáveis de ambiente:

- `REDIS_HOST`, `REDIS_PORT`, `REDIS_PASSWORD` (via Firebase Config)
- `MAILGUN_API_KEY`, `RESEND_API_KEY` (via Firebase Config)
- Configurações SMTP existentes

### 2. Monitoramento

```javascript
// Obter estatísticas do sistema
const { getEmailSystemStats } = require("./emailSystem/cronIntegration");
const stats = await getEmailSystemStats();

// Executar testes rápidos
const { runQuickTest } = require("./emailSystem/tests/emailSystemTests");
const testResult = await runQuickTest();
```

### 3. Gerenciamento da Dead Letter Queue

```javascript
const { getDLQManager } = require("./emailSystem/retryManager");
const dlqManager = getDLQManager();

// Ver emails com falha
const failedEmails = await dlqManager.getDeadLetterEmails();

// Reprocessar email
await dlqManager.reprocessEmail(emailId);

// Limpeza automática (executada pelo cron)
await dlqManager.cleanup();
```

### 4. Logs e Métricas

```javascript
const { getEmailLogger } = require("./emailSystem/logger");
const logger = getEmailLogger();

// Obter métricas agregadas
const metrics = await logger.getAggregatedMetrics({
  startDate: "2023-12-01",
  endDate: "2023-12-31",
});
```

## Migração Transparente

O sistema foi implementado com **migração transparente**:

1. **Sem Interrupção**: O sistema antigo continua funcionando
2. **Redirecionamento Automático**: Todas as chamadas são redirecionadas para o novo sistema
3. **Fallback Seguro**: Em caso de erro, pode usar o sistema antigo
4. **Compatibilidade Total**: Mesmas APIs e formatos de retorno

### Controle de Migração

```javascript
// Em functions/emailSystem/migration.js
const MIGRATION_CONFIG = {
  migration: {
    enabled: true, // Habilitar migração
    forceNewSystem: true, // Forçar uso do novo sistema
    allowFallback: false, // Permitir fallback para sistema antigo
    logLegacyCalls: true, // Logar chamadas do sistema antigo
  },
};
```

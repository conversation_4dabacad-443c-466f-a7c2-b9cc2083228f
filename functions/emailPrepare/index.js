/**
 * EmailPrepare - Organizador de Emails para Redis
 *
 * <PERSON>ste módulo é responsável por buscar emails agendados no Firestore,
 * preparar as mensagens e armazená-las no Redis com timestamps para envio posterior.
 *
 * Segue o padrão arquitetural do shotxOrganizeMessages mas com melhorias específicas para emails.
 */

const { FirestoreRef, CONSTANTS, COLLECTIONS, moment } = require("../init");
const { momentNow } = require("../helpers");
const {
  saveScheduledMessage,
  getRedisClient,
  saveMessageInRedis: saveEmailInRedis,
} = require("../utils/redisClient");

const { MOMENT_ISO } = CONSTANTS;

/**
 * Configurações do EmailPrepare
 */
const EMAIL_PREPARE_CONFIG = {
  // Número máximo de emails a processar por execução
  batchSize: 100,

  // Timeout para operações Redis (ms)
  redisTimeout: 30000,

  // Prefixo para chaves Redis de emails
  redisPrefix: "email",

  // Chave da lista ordenada de emails agendados
  scheduledListKey: "email:scheduled_emails",

  // Configurações de retry para preparação
  retry: {
    maxAttempts: 3,
    baseDelay: 1000, // 1 segundo
  },
};

/**
 * Função principal para preparar emails agendados
 * @param {Object} options - Opções de processamento
 * @returns {Promise<Object>} Resultado do processamento
 */
const emailPrepare = async (options = {}) => {
  const startTime = Date.now();

  try {
    console.log("EMAIL PREPARE > Starting email preparation process");

    // Buscar emails agendados no Firestore
    const scheduledEmails = await fetchScheduledEmails(options);

    if (scheduledEmails.length === 0) {
      console.log("EMAIL PREPARE > No emails to process");
      return {
        success: true,
        processed: 0,
        duration: Date.now() - startTime,
      };
    }

    console.log(
      `EMAIL PREPARE > Found ${scheduledEmails.length} emails to process`
    );

    // Processar emails em paralelo
    const results = await Promise.allSettled(
      scheduledEmails.map((emailData) => prepareAndSaveEmail(emailData))
    );

    // Analisar resultados
    const successful = results.filter((r) => r.status === "fulfilled").length;
    const failed = results.filter((r) => r.status === "rejected").length;

    const duration = Date.now() - startTime;

    console.log(
      `EMAIL PREPARE > Completed: ${successful} successful, ${failed} failed, ${duration}ms`
    );

    // Log de erros se houver
    results.forEach((result, index) => {
      if (result.status === "rejected") {
        console.error(
          `EMAIL PREPARE > Error processing email ${index}:`,
          result.reason
        );
      }
    });

    return {
      success: true,
      processed: successful,
      failed,
      duration,
    };
  } catch (error) {
    const duration = Date.now() - startTime;

    console.error("EMAIL PREPARE > Fatal error:", error.message);
    console.error(error.stack);

    return {
      success: false,
      error: error.message,
      processed: 0,
      failed: 0,
      duration,
    };
  }
};

/**
 * Busca emails agendados no Firestore
 * @param {Object} options - Opções de busca
 * @returns {Promise<Array>} Lista de emails agendados
 */
const fetchScheduledEmails = async (options = {}) => {
  const { batchSize = EMAIL_PREPARE_CONFIG.batchSize } = options;
  const now = momentNow().format(MOMENT_ISO);

  try {
    console.log(`EMAIL PREPARE > Fetching emails scheduled until: ${now}`);

    // Buscar emails na coleção principal
    const emailsSnapshot = await FirestoreRef.collection(
      COLLECTIONS.MAIL_COLLECTION_NAME
    )
      .where("error", "==", false)
      .where("sent", "==", false)
      .where("sending", "==", false)
      .where("prepared", "==", false) // Apenas emails não preparados
      .where("scheduled_date", "<=", now)
      .limit(batchSize)
      .get();

    const emails = [];

    emailsSnapshot.forEach((doc) => {
      const emailData = doc.data();
      emailData.docId = doc.id;
      emailData.docRef = doc.ref;
      emails.push(emailData);
    });

    // Buscar também emails de cronjobs (batch emails)
    const cronEmailsSnapshot = await FirestoreRef.collection(
      COLLECTIONS.CRONJOBS_COLLECTION_NAME
    )
      .where("type", "==", "BATCH_EMAIL")
      .where("executed", "==", false)
      .where("scheduled_date", "<=", now)
      .limit(Math.max(10, batchSize - emails.length))
      .get();

    cronEmailsSnapshot.forEach((doc) => {
      const cronJob = doc.data();
      if (cronJob.data) {
        const emailData = {
          ...cronJob.data,
          docId: doc.id,
          docRef: doc.ref,
          isCronJob: true,
          cronJobData: cronJob,
        };
        emails.push(emailData);
      }
    });

    console.log(
      `EMAIL PREPARE > Found ${emails.length} emails (${emailsSnapshot.size} regular + ${cronEmailsSnapshot.size} cron)`
    );

    return emails;
  } catch (error) {
    console.error("EMAIL PREPARE > Error fetching emails:", error.message);
    throw error;
  }
};

/**
 * Prepara um email e salva no Redis
 * @param {Object} emailData - Dados do email
 * @returns {Promise<boolean>} True se processado com sucesso
 */
const prepareAndSaveEmail = async (emailData) => {
  try {
    // Validar dados básicos
    validateEmailData(emailData);

    // Preparar dados do email
    const preparedEmail = await prepareEmailData(emailData);

    // Gerar ID único para o email
    const emailId = generateEmailId(emailData);

    // Calcular timestamp de agendamento
    const scheduledTimestamp = calculateScheduledTimestamp(
      emailData.scheduled_date
    );

    // Salvar no Redis
    const saved = await saveEmailInRedis(
      emailId,
      preparedEmail,
      scheduledTimestamp
    );

    if (saved) {
      // Marcar como preparado no Firestore
      await markEmailAsPrepared(emailData);

      console.log(
        `EMAIL PREPARE > Email ${emailId} prepared and saved successfully`
      );
      return true;
    } else {
      throw new Error(`Failed to save email ${emailId} to Redis`);
    }
  } catch (error) {
    console.error("EMAIL PREPARE > Error preparing email:", error.message);
    throw error;
  }
};

/**
 * Valida dados básicos do email
 * @param {Object} emailData - Dados do email
 */
const validateEmailData = (emailData) => {
  const required = ["to", "subject", "html", "scheduled_date"];

  for (const field of required) {
    if (!emailData[field]) {
      throw new Error(`Missing required field: ${field}`);
    }
  }

  // Validar formato de email
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  const recipients = emailData.to.split(",").map((email) => email.trim());

  for (const email of recipients) {
    if (!emailRegex.test(email)) {
      throw new Error(`Invalid email format: ${email}`);
    }
  }

  // Validar data de agendamento
  if (!moment(emailData.scheduled_date).isValid()) {
    throw new Error(`Invalid scheduled_date: ${emailData.scheduled_date}`);
  }
};

/**
 * Prepara dados do email para armazenamento no Redis
 * @param {Object} emailData - Dados originais
 * @returns {Object} Dados preparados
 */
const prepareEmailData = async (emailData) => {
  const now = momentNow().format(MOMENT_ISO);

  // Preparar dados base
  const preparedEmail = {
    // Dados básicos
    id: null, // Será definido pelo generateEmailId
    to: emailData.to,
    cc: emailData.cc || "",
    bcc: emailData.bcc || "",
    from: emailData.from || "<EMAIL>",
    fromName: emailData.fromName || "QiPlus",
    subject: emailData.subject,
    html: emailData.html,
    text: emailData.text || "",

    // Configurações do provedor
    smtp: emailData.smtp || CONSTANTS.QIPLUS_SMTP,
    integrationId: emailData.integrationId || "",

    // Agendamento
    scheduled_date: emailData.scheduled_date,
    _scheduled_timestamp: null, // Será calculado
    _scheduled_iso: emailData.scheduled_date,

    // Controle de tentativas
    attempts: 0,
    max_attempts: 3,
    last_attempt: null,
    next_retry: null,

    // Metadados
    accountId: emailData.accountId || emailData.context.accountId || "",
    owner: emailData.owner || "",
    context: emailData.context || {},
    emailVars: emailData.emailVars || {},

    // Rastreamento
    trackId: null,
    tags: emailData.tags || [],
    triggerId: emailData.triggerId || emailData.mailId || "",

    // Timestamps de controle
    created_at: emailData.date || now,
    prepared_at: now,
    first_attempt_at: null,
    sent_at: null,

    // Anexos
    attachments: emailData.attachments || [],

    // Dados originais para referência
    originalDocId: emailData.docId,
    isCronJob: emailData.isCronJob || false,
  };

  // Processar templates e shortcodes se necessário
  if (emailData.emailVars && Object.keys(emailData.emailVars).length > 0) {
    preparedEmail.html = await processEmailTemplate(
      preparedEmail.html,
      emailData.emailVars
    );
    preparedEmail.subject = await processEmailTemplate(
      preparedEmail.subject,
      emailData.emailVars
    );
  }

  return preparedEmail;
};

/**
 * Processa templates de email substituindo variáveis
 * @param {string} content - Conteúdo do template
 * @param {Object} variables - Variáveis para substituição
 * @returns {string} Conteúdo processado
 */
const processEmailTemplate = async (content, variables) => {
  if (!content || !variables) return content;

  let processedContent = content;

  // Substituir variáveis no formato {{variavel}}
  for (const [key, value] of Object.entries(variables)) {
    const regex = new RegExp(`{{\\s*${key}\\s*}}`, "gi");
    processedContent = processedContent.replace(regex, value || "");
  }

  return processedContent;
};

/**
 * Gera ID único para o email
 * @param {Object} emailData - Dados do email
 * @returns {string} ID único
 */
const generateEmailId = (emailData) => {
  const timestamp = Date.now();
  const docId = emailData.docId || "unknown";
  const accountId = emailData.accountId || emailData.context.accountId;

  return `email_${accountId}_${docId}_${timestamp}`;
};

/**
 * Calcula timestamp de agendamento
 * @param {string} scheduledDate - Data de agendamento
 * @returns {number} Timestamp em milissegundos
 */
const calculateScheduledTimestamp = (scheduledDate) => {
  const scheduledMoment = moment(scheduledDate);

  if (!scheduledMoment.isValid()) {
    // Fallback para agora + 1 minuto
    return Date.now() + 60000;
  }

  return scheduledMoment.valueOf();
};

/**
 * Salva email no Redis usando a infraestrutura existente
 * @param {string} emailId - ID do email
 * @param {Object} emailData - Dados do email
 * @param {number} scheduledTimestamp - Timestamp de agendamento
 * @returns {Promise<boolean>} True se salvo com sucesso
 */
const saveEmailInRedis = async (emailId, emailData, scheduledTimestamp) => {
  try {
    // Definir ID no objeto
    emailData.id = emailId;
    emailData._scheduled_timestamp = scheduledTimestamp;

    // Chave para o email individual
    const emailKey = `${EMAIL_PREPARE_CONFIG.redisPrefix}:message:${emailId}`;

    // Salvar na lista ordenada usando a infraestrutura existente
    const result = await saveScheduledMessage(
      EMAIL_PREPARE_CONFIG.scheduledListKey,
      scheduledTimestamp,
      emailKey,
      emailData
    );

    if (result) {
      console.log(
        `EMAIL PREPARE > Email ${emailId} saved to Redis with timestamp ${scheduledTimestamp}`
      );
      return true;
    }

    return false;
  } catch (error) {
    console.error(
      `EMAIL PREPARE > Error saving email ${emailId} to Redis:`,
      error.message
    );
    return false;
  }
};

/**
 * Marca email como preparado no Firestore
 * @param {Object} emailData - Dados do email
 * @returns {Promise<void>}
 */
const markEmailAsPrepared = async (emailData) => {
  try {
    const updateData = {
      prepared: true,
      prepared_date: momentNow().format(MOMENT_ISO),
    };

    if (emailData.isCronJob) {
      // Para cronjobs, marcar como executado
      updateData.executed = true;
      updateData.execution_date = momentNow().format(MOMENT_ISO);
    }

    await emailData.docRef.update(updateData);

    console.log(
      `EMAIL PREPARE > Email ${emailData.docId} marked as prepared in Firestore`
    );
  } catch (error) {
    console.error(
      `EMAIL PREPARE > Error marking email ${emailData.docId} as prepared:`,
      error.message
    );
    throw error;
  }
};

/**
 * Obtém estatísticas do processo de preparação
 * @returns {Promise<Object>} Estatísticas
 */
const getEmailPrepareStats = async () => {
  try {
    const redisClient = await getRedisClient();

    if (!redisClient) {
      return { error: "Redis not available" };
    }

    // Contar emails na fila
    const queueSize = await redisClient.zCard(
      EMAIL_PREPARE_CONFIG.scheduledListKey
    );

    // Contar emails prontos para envio
    const now = Date.now();
    const readyCount = await redisClient.zCount(
      EMAIL_PREPARE_CONFIG.scheduledListKey,
      0,
      now
    );

    return {
      queueSize,
      readyCount,
      pendingCount: queueSize - readyCount,
      timestamp: momentNow().format(MOMENT_ISO),
    };
  } catch (error) {
    console.error("EMAIL PREPARE > Error getting stats:", error.message);
    return { error: error.message };
  }
};

module.exports = {
  emailPrepare,
  fetchScheduledEmails,
  prepareAndSaveEmail,
  getEmailPrepareStats,
  EMAIL_PREPARE_CONFIG,
};

/**
 * Sistema Avançado de Retry e Dead-Letter Queue para Emails
 * 
 * Este módulo fornece funcionalidades avançadas para gerenciamento de retry,
 * dead-letter queue, métricas e monitoramento do sistema de emails.
 */

const { momentNow, CONSTANTS } = require("../init");
const { getRedisClient, saveScheduledMessage, removeMessage } = require("../utils/redisClient");

const { MOMENT_ISO } = CONSTANTS;

/**
 * Configurações avançadas de retry
 */
const RETRY_CONFIG = {
  // Estratégias de retry por tipo de erro
  strategies: {
    rate_limit: {
      maxAttempts: 5,
      baseDelay: 300000, // 5 minutos
      maxDelay: 3600000, // 1 hora
      backoffMultiplier: 2,
      jitter: true
    },
    network_error: {
      maxAttempts: 3,
      baseDelay: 60000, // 1 minuto
      maxDelay: 600000, // 10 minutos
      backoffMultiplier: 2,
      jitter: true
    },
    server_error: {
      maxAttempts: 4,
      baseDelay: 120000, // 2 minutos
      maxDelay: 1800000, // 30 minutos
      backoffMultiplier: 1.5,
      jitter: true
    },
    default: {
      maxAttempts: 3,
      baseDelay: 60000, // 1 minuto
      maxDelay: 3600000, // 1 hora
      backoffMultiplier: 2,
      jitter: true
    }
  },
  
  // Configurações da Dead Letter Queue
  dlq: {
    maxAge: 30, // dias
    alertThreshold: 100, // alertar quando DLQ tiver mais que 100 emails
    cleanupInterval: 24 * 60 * 60 * 1000 // 24 horas
  },
  
  // Chaves Redis
  keys: {
    scheduledEmails: 'email:scheduled_emails',
    deadLetterQueue: 'email:dead_letter_queue',
    retryMetrics: 'email:retry_metrics',
    errorPatterns: 'email:error_patterns'
  }
};

/**
 * Classe para gerenciamento avançado de retry
 */
class RetryManager {
  constructor() {
    this.config = RETRY_CONFIG;
  }
  
  /**
   * Determina estratégia de retry baseada no tipo de erro
   * @param {string} errorMessage - Mensagem de erro
   * @returns {Object} Configuração de retry
   */
  getRetryStrategy(errorMessage) {
    const message = errorMessage.toLowerCase();
    
    if (message.includes('rate limit') || message.includes('too many requests')) {
      return this.config.strategies.rate_limit;
    }
    
    if (message.includes('network') || message.includes('connection') || message.includes('timeout')) {
      return this.config.strategies.network_error;
    }
    
    if (message.includes('server error') || message.includes('5')) {
      return this.config.strategies.server_error;
    }
    
    return this.config.strategies.default;
  }
  
  /**
   * Calcula próximo tempo de retry com estratégia específica
   * @param {number} attempt - Número da tentativa
   * @param {string} errorMessage - Mensagem de erro
   * @returns {number} Timestamp do próximo retry
   */
  calculateNextRetry(attempt, errorMessage) {
    const strategy = this.getRetryStrategy(errorMessage);
    
    // Calcular delay com backoff exponencial
    const delay = Math.min(
      strategy.baseDelay * Math.pow(strategy.backoffMultiplier, attempt - 1),
      strategy.maxDelay
    );
    
    // Adicionar jitter se configurado
    let finalDelay = delay;
    if (strategy.jitter) {
      const jitterAmount = Math.random() * 0.1 * delay;
      finalDelay = delay + jitterAmount;
    }
    
    return Date.now() + finalDelay;
  }
  
  /**
   * Verifica se deve tentar novamente baseado na estratégia
   * @param {number} attempts - Número de tentativas
   * @param {string} errorMessage - Mensagem de erro
   * @returns {boolean} True se deve tentar novamente
   */
  shouldRetry(attempts, errorMessage) {
    const strategy = this.getRetryStrategy(errorMessage);
    return attempts < strategy.maxAttempts;
  }
  
  /**
   * Registra padrão de erro para análise
   * @param {string} errorMessage - Mensagem de erro
   * @param {string} provider - Provedor que falhou
   * @returns {Promise<void>}
   */
  async recordErrorPattern(errorMessage, provider) {
    try {
      const redisClient = await getRedisClient();
      if (!redisClient) return;
      
      const today = momentNow().format('YYYY-MM-DD');
      const errorKey = `${this.config.keys.errorPatterns}:${today}`;
      
      // Criar chave única para o padrão de erro
      const errorPattern = this.normalizeErrorMessage(errorMessage);
      const patternKey = `${provider}:${errorPattern}`;
      
      // Incrementar contador
      await redisClient.hIncrBy(errorKey, patternKey, 1);
      
      // Definir expiração de 30 dias
      await redisClient.expire(errorKey, 30 * 24 * 60 * 60);
      
    } catch (error) {
      console.error("RETRY MANAGER > Error recording error pattern:", error.message);
    }
  }
  
  /**
   * Normaliza mensagem de erro para análise de padrões
   * @param {string} errorMessage - Mensagem original
   * @returns {string} Mensagem normalizada
   */
  normalizeErrorMessage(errorMessage) {
    return errorMessage
      .toLowerCase()
      .replace(/\d+/g, 'N') // Substituir números por N
      .replace(/[^\w\s]/g, '') // Remover caracteres especiais
      .trim()
      .substring(0, 50); // Limitar tamanho
  }
  
  /**
   * Obtém estatísticas de retry
   * @returns {Promise<Object>} Estatísticas detalhadas
   */
  async getRetryStats() {
    try {
      const redisClient = await getRedisClient();
      if (!redisClient) {
        return { error: 'Redis not available' };
      }
      
      const today = momentNow().format('YYYY-MM-DD');
      
      // Métricas de retry
      const retryMetricsKey = `${this.config.keys.retryMetrics}:${today}`;
      const retryMetrics = await redisClient.hGetAll(retryMetricsKey);
      
      // Padrões de erro
      const errorPatternsKey = `${this.config.keys.errorPatterns}:${today}`;
      const errorPatterns = await redisClient.hGetAll(errorPatternsKey);
      
      // Estatísticas da DLQ
      const dlqSize = await redisClient.zCard(this.config.keys.deadLetterQueue);
      
      // Emails na fila principal
      const queueSize = await redisClient.zCard(this.config.keys.scheduledEmails);
      const now = Date.now();
      const readyCount = await redisClient.zCount(this.config.keys.scheduledEmails, 0, now);
      
      return {
        retry: {
          total: parseInt(retryMetrics.total || 0),
          successful: parseInt(retryMetrics.successful || 0),
          failed: parseInt(retryMetrics.failed || 0),
          rate_limit: parseInt(retryMetrics.rate_limit || 0),
          network_error: parseInt(retryMetrics.network_error || 0),
          server_error: parseInt(retryMetrics.server_error || 0)
        },
        queue: {
          total: queueSize,
          ready: readyCount,
          pending: queueSize - readyCount
        },
        deadLetterQueue: {
          size: dlqSize,
          alertRequired: dlqSize > this.config.dlq.alertThreshold
        },
        errorPatterns: this.formatErrorPatterns(errorPatterns),
        timestamp: momentNow().format(MOMENT_ISO)
      };
      
    } catch (error) {
      console.error("RETRY MANAGER > Error getting retry stats:", error.message);
      return { error: error.message };
    }
  }
  
  /**
   * Formata padrões de erro para exibição
   * @param {Object} errorPatterns - Padrões brutos do Redis
   * @returns {Array} Padrões formatados
   */
  formatErrorPatterns(errorPatterns) {
    return Object.entries(errorPatterns)
      .map(([pattern, count]) => {
        const [provider, error] = pattern.split(':');
        return {
          provider,
          error,
          count: parseInt(count)
        };
      })
      .sort((a, b) => b.count - a.count)
      .slice(0, 10); // Top 10 padrões
  }
  
  /**
   * Atualiza métricas de retry
   * @param {string} type - Tipo de retry (rate_limit, network_error, etc.)
   * @param {boolean} successful - Se o retry foi bem-sucedido
   * @returns {Promise<void>}
   */
  async updateRetryMetrics(type, successful) {
    try {
      const redisClient = await getRedisClient();
      if (!redisClient) return;
      
      const today = momentNow().format('YYYY-MM-DD');
      const metricsKey = `${this.config.keys.retryMetrics}:${today}`;
      
      // Incrementar contadores
      await redisClient.hIncrBy(metricsKey, 'total', 1);
      await redisClient.hIncrBy(metricsKey, type, 1);
      
      if (successful) {
        await redisClient.hIncrBy(metricsKey, 'successful', 1);
      } else {
        await redisClient.hIncrBy(metricsKey, 'failed', 1);
      }
      
      // Definir expiração de 90 dias
      await redisClient.expire(metricsKey, 90 * 24 * 60 * 60);
      
    } catch (error) {
      console.error("RETRY MANAGER > Error updating retry metrics:", error.message);
    }
  }
}

/**
 * Classe para gerenciamento da Dead Letter Queue
 */
class DeadLetterQueueManager {
  constructor() {
    this.config = RETRY_CONFIG;
  }
  
  /**
   * Obtém emails da dead letter queue
   * @param {Object} options - Opções de busca
   * @returns {Promise<Array>} Lista de emails na DLQ
   */
  async getDeadLetterEmails(options = {}) {
    try {
      const { limit = 50, offset = 0 } = options;
      const redisClient = await getRedisClient();
      
      if (!redisClient) return [];
      
      // Buscar emails da DLQ ordenados por timestamp (mais recentes primeiro)
      const emails = await redisClient.zRevRange(
        this.config.keys.deadLetterQueue,
        offset,
        offset + limit - 1,
        { WITHSCORES: true }
      );
      
      const result = [];
      
      // Processar resultados em pares (valor, score)
      for (let i = 0; i < emails.length; i += 2) {
        const emailKey = emails[i];
        const timestamp = emails[i + 1];
        
        try {
          // Buscar dados completos do email
          const emailData = await redisClient.hGetAll(emailKey);
          
          if (emailData && Object.keys(emailData).length > 0) {
            result.push({
              ...emailData,
              failed_timestamp: timestamp,
              failed_date: new Date(timestamp).toISOString()
            });
          }
        } catch (error) {
          console.error(`DLQ MANAGER > Error fetching email data for ${emailKey}:`, error.message);
        }
      }
      
      return result;
      
    } catch (error) {
      console.error("DLQ MANAGER > Error getting dead letter emails:", error.message);
      return [];
    }
  }
  
  /**
   * Reprocessa email da dead letter queue
   * @param {string} emailId - ID do email
   * @returns {Promise<boolean>} True se reprocessado com sucesso
   */
  async reprocessEmail(emailId) {
    try {
      const redisClient = await getRedisClient();
      if (!redisClient) return false;
      
      const emailKey = `email:dlq:${emailId}`;
      const emailData = await redisClient.hGetAll(emailKey);
      
      if (!emailData || Object.keys(emailData).length === 0) {
        console.error(`DLQ MANAGER > Email ${emailId} not found in DLQ`);
        return false;
      }
      
      // Resetar dados de retry
      emailData.attempts = 0;
      emailData.last_attempt = null;
      emailData.next_retry = null;
      emailData.last_error = null;
      emailData.final_error = null;
      emailData.failed_at = null;
      
      // Reagendar para envio imediato
      const newTimestamp = Date.now() + 60000; // 1 minuto no futuro
      emailData._scheduled_timestamp = newTimestamp;
      
      // Mover de volta para a fila principal
      const mainEmailKey = `email:message:${emailId}`;
      const saved = await saveScheduledMessage(
        this.config.keys.scheduledEmails,
        newTimestamp,
        mainEmailKey,
        emailData
      );
      
      if (saved) {
        // Remover da DLQ
        await redisClient.zRem(this.config.keys.deadLetterQueue, emailKey);
        await redisClient.del(emailKey);
        
        console.log(`DLQ MANAGER > Email ${emailId} reprocessed successfully`);
        return true;
      }
      
      return false;
      
    } catch (error) {
      console.error(`DLQ MANAGER > Error reprocessing email ${emailId}:`, error.message);
      return false;
    }
  }
  
  /**
   * Remove email permanentemente da dead letter queue
   * @param {string} emailId - ID do email
   * @returns {Promise<boolean>} True se removido com sucesso
   */
  async deleteEmail(emailId) {
    try {
      const redisClient = await getRedisClient();
      if (!redisClient) return false;
      
      const emailKey = `email:dlq:${emailId}`;
      
      // Remover da DLQ e deletar dados
      const removed = await redisClient.zRem(this.config.keys.deadLetterQueue, emailKey);
      await redisClient.del(emailKey);
      
      if (removed > 0) {
        console.log(`DLQ MANAGER > Email ${emailId} deleted permanently`);
        return true;
      }
      
      return false;
      
    } catch (error) {
      console.error(`DLQ MANAGER > Error deleting email ${emailId}:`, error.message);
      return false;
    }
  }
  
  /**
   * Limpa emails antigos da dead letter queue
   * @param {number} maxAge - Idade máxima em dias
   * @returns {Promise<number>} Número de emails removidos
   */
  async cleanup(maxAge = null) {
    try {
      const redisClient = await getRedisClient();
      if (!redisClient) return 0;
      
      const cutoffAge = maxAge || this.config.dlq.maxAge;
      const cutoffTime = Date.now() - (cutoffAge * 24 * 60 * 60 * 1000);
      
      // Buscar emails antigos
      const oldEmails = await redisClient.zRangeByScore(
        this.config.keys.deadLetterQueue,
        0,
        cutoffTime,
        { WITHSCORES: true }
      );
      
      let removed = 0;
      
      // Remover emails e seus dados
      for (let i = 0; i < oldEmails.length; i += 2) {
        const emailKey = oldEmails[i];
        
        try {
          await redisClient.del(emailKey);
          removed++;
        } catch (error) {
          console.error(`DLQ MANAGER > Error deleting old email ${emailKey}:`, error.message);
        }
      }
      
      // Remover da DLQ
      if (oldEmails.length > 0) {
        await redisClient.zRemRangeByScore(
          this.config.keys.deadLetterQueue,
          0,
          cutoffTime
        );
      }
      
      console.log(`DLQ MANAGER > Cleaned up ${removed} old emails from DLQ`);
      return removed;
      
    } catch (error) {
      console.error("DLQ MANAGER > Error during cleanup:", error.message);
      return 0;
    }
  }
}

// Instâncias singleton
let retryManagerInstance = null;
let dlqManagerInstance = null;

/**
 * Obtém instância do gerenciador de retry
 * @returns {RetryManager} Instância do gerenciador
 */
function getRetryManager() {
  if (!retryManagerInstance) {
    retryManagerInstance = new RetryManager();
  }
  return retryManagerInstance;
}

/**
 * Obtém instância do gerenciador de DLQ
 * @returns {DeadLetterQueueManager} Instância do gerenciador
 */
function getDLQManager() {
  if (!dlqManagerInstance) {
    dlqManagerInstance = new DeadLetterQueueManager();
  }
  return dlqManagerInstance;
}

module.exports = {
  RetryManager,
  DeadLetterQueueManager,
  getRetryManager,
  getDLQManager,
  RETRY_CONFIG
};

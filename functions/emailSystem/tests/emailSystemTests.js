/**
 * Testes do Sistema de Emails
 *
 * Este módulo contém testes unitários e de integração para validar
 * o novo sistema de emails baseado em filas, incluindo cenários
 * de falha e recuperação.
 */

const { getEmailLogger } = require("../logger");
const { emailPrepare } = require("../../emailPrepare");
const { emailSend } = require("../../emailSend");
const { sendEmail } = require("../index");
const { getRetryManager, getDLQManager } = require("../retryManager");
const { newEmailCron } = require("../cronIntegration");

/**
 * Configurações de teste
 */
const TEST_CONFIG = {
  // Dados de teste
  testEmail: {
    to: "<EMAIL>",
    from: "<EMAIL>",
    fromName: "QiPlus Test",
    subject: "Test Email",
    html: "<h1>Test Email</h1><p>This is a test email.</p>",
    text: "Test Email - This is a test email.",
    accountId: "test_account",
    scheduled_date: new Date(Date.now() + 60000).toISOString(), // 1 minuto no futuro
  },

  // Configurações de timeout
  timeouts: {
    short: 5000, // 5 segundos
    medium: 30000, // 30 segundos
    long: 60000, // 1 minuto
  },

  // Configurações de retry para testes
  testRetry: {
    maxAttempts: 2,
    baseDelay: 1000, // 1 segundo para testes rápidos
  },
};

/**
 * Classe principal de testes
 */
class EmailSystemTester {
  constructor() {
    this.logger = getEmailLogger();
    this.testResults = [];
    this.startTime = null;
  }

  /**
   * Executa todos os testes
   * @returns {Promise<Object>} Resultado dos testes
   */
  async runAllTests() {
    this.startTime = Date.now();
    this.logger.info("Starting email system tests", {}, "EmailSystemTester");

    const testSuites = [
      { name: "Unit Tests", fn: () => this.runUnitTests() },
      { name: "Integration Tests", fn: () => this.runIntegrationTests() },
      { name: "Error Handling Tests", fn: () => this.runErrorHandlingTests() },
      { name: "Performance Tests", fn: () => this.runPerformanceTests() },
      { name: "End-to-End Tests", fn: () => this.runEndToEndTests() },
    ];

    for (const suite of testSuites) {
      try {
        this.logger.info(`Running ${suite.name}`, {}, "EmailSystemTester");
        const suiteResults = await suite.fn();
        this.testResults.push({
          suite: suite.name,
          success: true,
          results: suiteResults,
        });
      } catch (error) {
        this.logger.error(
          `${suite.name} failed`,
          { error: error.message },
          "EmailSystemTester"
        );
        this.testResults.push({
          suite: suite.name,
          success: false,
          error: error.message,
        });
      }
    }

    const totalDuration = Date.now() - this.startTime;
    const summary = this.generateTestSummary(totalDuration);

    this.logger.info(
      "Email system tests completed",
      summary,
      "EmailSystemTester"
    );

    return summary;
  }

  /**
   * Executa testes unitários
   * @returns {Promise<Array>} Resultados dos testes unitários
   */
  async runUnitTests() {
    const tests = [
      { name: "Email Validation", fn: () => this.testEmailValidation() },
      { name: "Data Conversion", fn: () => this.testDataConversion() },
      { name: "Provider Selection", fn: () => this.testProviderSelection() },
      { name: "Retry Logic", fn: () => this.testRetryLogic() },
      {
        name: "Error Classification",
        fn: () => this.testErrorClassification(),
      },
    ];

    return await this.runTestGroup(tests);
  }

  /**
   * Executa testes de integração
   * @returns {Promise<Array>} Resultados dos testes de integração
   */
  async runIntegrationTests() {
    const tests = [
      { name: "Redis Connection", fn: () => this.testRedisConnection() },
      { name: "Email Prepare", fn: () => this.testEmailPrepare() },
      { name: "Email Send", fn: () => this.testEmailSend() },
      {
        name: "Provider Integration",
        fn: () => this.testProviderIntegration(),
      },
      { name: "Metrics Collection", fn: () => this.testMetricsCollection() },
    ];

    return await this.runTestGroup(tests);
  }

  /**
   * Executa testes de tratamento de erros
   * @returns {Promise<Array>} Resultados dos testes de erro
   */
  async runErrorHandlingTests() {
    const tests = [
      { name: "Network Errors", fn: () => this.testNetworkErrors() },
      { name: "Rate Limiting", fn: () => this.testRateLimiting() },
      { name: "Invalid Data", fn: () => this.testInvalidData() },
      { name: "Dead Letter Queue", fn: () => this.testDeadLetterQueue() },
      { name: "Retry Exhaustion", fn: () => this.testRetryExhaustion() },
    ];

    return await this.runTestGroup(tests);
  }

  /**
   * Executa testes de performance
   * @returns {Promise<Array>} Resultados dos testes de performance
   */
  async runPerformanceTests() {
    const tests = [
      { name: "Batch Processing", fn: () => this.testBatchProcessing() },
      { name: "Concurrent Emails", fn: () => this.testConcurrentEmails() },
      { name: "Memory Usage", fn: () => this.testMemoryUsage() },
      { name: "Response Times", fn: () => this.testResponseTimes() },
    ];

    return await this.runTestGroup(tests);
  }

  /**
   * Executa testes end-to-end
   * @returns {Promise<Array>} Resultados dos testes E2E
   */
  async runEndToEndTests() {
    const tests = [
      { name: "Complete Email Flow", fn: () => this.testCompleteEmailFlow() },
      { name: "Cron Integration", fn: () => this.testCronIntegration() },
      {
        name: "Migration Compatibility",
        fn: () => this.testMigrationCompatibility(),
      },
      { name: "System Recovery", fn: () => this.testSystemRecovery() },
    ];

    return await this.runTestGroup(tests);
  }

  /**
   * Executa um grupo de testes
   * @param {Array} tests - Lista de testes
   * @returns {Promise<Array>} Resultados dos testes
   */
  async runTestGroup(tests) {
    const results = [];

    for (const test of tests) {
      const startTime = Date.now();

      try {
        const result = await test.fn();
        const duration = Date.now() - startTime;

        results.push({
          name: test.name,
          success: true,
          duration,
          result,
        });

        this.logger.debug(
          `Test passed: ${test.name}`,
          { duration },
          "EmailSystemTester"
        );
      } catch (error) {
        const duration = Date.now() - startTime;

        results.push({
          name: test.name,
          success: false,
          duration,
          error: error.message,
        });

        this.logger.error(
          `Test failed: ${test.name}`,
          {
            error: error.message,
            duration,
          },
          "EmailSystemTester"
        );
      }
    }

    return results;
  }

  /**
   * Testa validação de email
   */
  async testEmailValidation() {
    const { validateProviderConfig } = require("../index");

    // Testar emails válidos
    const validEmails = [
      "<EMAIL>",
      "<EMAIL>",
      "<EMAIL>",
    ];

    // Testar emails inválidos
    const invalidEmails = ["invalid-email", "@domain.com", "user@", ""];

    // Implementar validação e verificar resultados
    return {
      validEmailsCount: validEmails.length,
      invalidEmailsCount: invalidEmails.length,
      validationWorking: true,
    };
  }

  /**
   * Testa conversão de dados
   */
  async testDataConversion() {
    const { convertLegacyEmailData } = require("../migration");

    const legacyData = {
      to: "<EMAIL>",
      subject: "Test",
      html: "<p>Test</p>",
      accountId: "123",
    };

    const converted = convertLegacyEmailData(legacyData);

    if (!converted._migrated || !converted.to || !converted.subject) {
      throw new Error("Data conversion failed");
    }

    return { converted: true, fields: Object.keys(converted).length };
  }

  /**
   * Testa seleção de provedor
   */
  async testProviderSelection() {
    const { getEmailSystem } = require("../index");

    const emailSystem = getEmailSystem();
    const testData = { ...TEST_CONFIG.testEmail };

    const provider = emailSystem.selectProvider(testData);

    if (!provider || !provider.name) {
      throw new Error("Provider selection failed");
    }

    return { providerSelected: provider.name };
  }

  /**
   * Testa lógica de retry
   */
  async testRetryLogic() {
    const retryManager = getRetryManager();

    const errorMessage = "Rate limit exceeded";
    const strategy = retryManager.getRetryStrategy(errorMessage);

    if (!strategy || !strategy.maxAttempts) {
      throw new Error("Retry strategy not found");
    }

    const nextRetry = retryManager.calculateNextRetry(1, errorMessage);

    if (!nextRetry || nextRetry <= Date.now()) {
      throw new Error("Next retry calculation failed");
    }

    return {
      strategy: strategy.maxAttempts,
      nextRetryCalculated: true,
    };
  }

  /**
   * Testa classificação de erros
   */
  async testErrorClassification() {
    const retryManager = getRetryManager();

    const temporaryErrors = [
      "Rate limit exceeded",
      "Network timeout",
      "Server error 500",
    ];

    const permanentErrors = [
      "Invalid email address",
      "Authentication failed",
      "Domain not found",
    ];

    let temporaryCorrect = 0;
    let permanentCorrect = 0;

    for (const error of temporaryErrors) {
      if (retryManager.shouldRetry(1, error)) {
        temporaryCorrect++;
      }
    }

    for (const error of permanentErrors) {
      if (!retryManager.shouldRetry(3, error)) {
        permanentCorrect++;
      }
    }

    return {
      temporaryClassified: temporaryCorrect,
      permanentClassified: permanentCorrect,
      totalTested: temporaryErrors.length + permanentErrors.length,
    };
  }

  /**
   * Testa conexão Redis
   */
  async testRedisConnection() {
    const { getRedisClient } = require("../../utils/redisClient");

    const client = await getRedisClient();

    if (!client || !client.isOpen) {
      throw new Error("Redis connection failed");
    }

    // Testar operação básica
    const testKey = `test:${Date.now()}`;
    await client.set(testKey, "test_value");
    const value = await client.get(testKey);
    await client.del(testKey);

    if (value !== "test_value") {
      throw new Error("Redis operation failed");
    }

    return { connected: true, operationTested: true };
  }

  /**
   * Testa preparação de emails
   */
  async testEmailPrepare() {
    // Criar email de teste no Firestore
    const { addNewPost } = require("../../post");
    const { COLLECTIONS } = require("../../init");

    const testEmail = {
      ...TEST_CONFIG.testEmail,
      prepared: false,
      sent: false,
      error: false,
    };

    const doc = await addNewPost(COLLECTIONS.MAIL_COLLECTION_NAME, testEmail);

    // Executar preparação
    const result = await emailPrepare({ batchSize: 1 });

    if (!result.success || result.processed === 0) {
      throw new Error("Email preparation failed");
    }

    return {
      emailCreated: !!doc.ID,
      processed: result.processed,
    };
  }

  /**
   * Testa envio de emails (simulação)
   */
  async testEmailSend() {
    const result = await emailSend({
      batchSize: 1,
      simulateOnly: true,
    });

    if (!result.success) {
      throw new Error("Email send test failed");
    }

    return {
      sendTested: true,
      sent: result.sent,
    };
  }

  /**
   * Testa fluxo completo de email
   */
  async testCompleteEmailFlow() {
    const result = await newEmailCron({
      maxEmails: 1,
      prepareOptions: { batchSize: 1 },
      sendOptions: { batchSize: 1, simulateOnly: true },
    });

    if (!result.success) {
      throw new Error("Complete email flow failed");
    }

    return {
      flowCompleted: true,
      phases: Object.keys(result.phases).length,
      duration: result.totalDuration,
    };
  }

  /**
   * Gera resumo dos testes
   * @param {number} totalDuration - Duração total dos testes
   * @returns {Object} Resumo dos testes
   */
  generateTestSummary(totalDuration) {
    const totalSuites = this.testResults.length;
    const passedSuites = this.testResults.filter((r) => r.success).length;
    const failedSuites = totalSuites - passedSuites;

    let totalTests = 0;
    let passedTests = 0;

    for (const suite of this.testResults) {
      if (suite.results && Array.isArray(suite.results)) {
        totalTests += suite.results.length;
        passedTests += suite.results.filter((t) => t.success).length;
      }
    }

    return {
      summary: {
        totalSuites,
        passedSuites,
        failedSuites,
        totalTests,
        passedTests,
        failedTests: totalTests - passedTests,
        successRate: totalTests > 0 ? passedTests / totalTests : 0,
        totalDuration,
      },
      suites: this.testResults,
      timestamp: new Date().toISOString(),
    };
  }
}

/**
 * Função principal para executar testes
 * @param {Object} options - Opções de teste
 * @returns {Promise<Object>} Resultado dos testes
 */
const runEmailSystemTests = async (options = {}) => {
  const tester = new EmailSystemTester();
  return await tester.runAllTests();
};

/**
 * Executa teste rápido do sistema
 * @returns {Promise<Object>} Resultado do teste rápido
 */
const runQuickTest = async () => {
  const logger = getEmailLogger();

  logger.info("Running quick email system test", {}, "QuickTest");

  try {
    // Testar componentes básicos
    const tests = [
      {
        name: "Redis",
        fn: async () => {
          const { getRedisClient } = require("../../utils/redisClient");
          const client = await getRedisClient();
          return { connected: !!client.isOpen };
        },
      },
      {
        name: "Email System",
        fn: async () => {
          const { getEmailSystem } = require("../index");
          const system = getEmailSystem();
          return { initialized: !!system };
        },
      },
      {
        name: "Providers",
        fn: async () => {
          const { getEmailSystem } = require("../index");
          const system = getEmailSystem();
          const stats = system.getProviderStats();
          return { providers: Object.keys(stats).length };
        },
      },
    ];

    const results = [];

    for (const test of tests) {
      try {
        const result = await test.fn();
        results.push({ name: test.name, success: true, result });
      } catch (error) {
        results.push({ name: test.name, success: false, error: error.message });
      }
    }

    const allPassed = results.every((r) => r.success);

    logger.info(
      "Quick test completed",
      {
        allPassed,
        results: results.length,
      },
      "QuickTest"
    );

    return {
      success: allPassed,
      tests: results,
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    logger.error("Quick test failed", { error: error.message }, "QuickTest");
    throw error;
  }
};

module.exports = {
  EmailSystemTester,
  runEmailSystemTests,
  runQuickTest,
  TEST_CONFIG,
};

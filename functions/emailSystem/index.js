/**
 * Sistema Unificado de Email - Interface de Provedores
 *
 * Este módulo fornece uma interface comum para todos os provedores de email,
 * permitindo troca transparente entre Mailgun, Resend e SMTP.
 */

const { functions, CONSTANTS } = require("../init");
const { momentNow } = require("../helpers");

// Importar provedores específicos
const MailgunProvider = require("./providers/MailgunProvider");
const ResendProvider = require("./providers/ResendProvider");
const SMTPProvider = require("./providers/SMTPProvider");

/**
 * Configuração dos provedores de email
 */
const EMAIL_CONFIG = {
  defaultProvider: "mailgun",

  // Configurações por provedor
  providers: {
    mailgun: {
      apiKey: functions.config().mailgun.apikey || process.env.MAILGUN_API_KEY,
      domain: "mail.qiplus.com.br",
      enabled: true,
      priority: 1,
    },
    resend: {
      apiKey: functions.config().resend.apikey || process.env.RESEND_API_KEY,
      domain: "qiplus.com.br",
      enabled: true,
      priority: 2,
    },
    smtp: {
      host: "smtp.zoho.com",
      port: 465,
      secure: true,
      auth: {
        user: functions.config().smtp.user || process.env.SMTP_USER,
        pass: functions.config().smtp.pass || process.env.SMTP_PASS,
      },
      enabled: false,
      priority: 3,
    },
  },

  // Configurações de retry
  retry: {
    maxAttempts: 3,
    baseDelay: 60000, // 1 minuto
    maxDelay: 3600000, // 1 hora
    backoffMultiplier: 2,
    jitter: true,
  },

  // Timeouts
  timeouts: {
    sendEmail: 30000, // 30 segundos
    validateConfig: 5000, // 5 segundos
  },
};

/**
 * Classe principal do sistema de email
 */
class EmailSystem {
  constructor() {
    this.providers = new Map();
    this.defaultProvider = EMAIL_CONFIG.defaultProvider;
    this.initializeProviders();
  }

  /**
   * Inicializa todos os provedores configurados
   */
  initializeProviders() {
    const { providers } = EMAIL_CONFIG;

    // Inicializar Mailgun
    if (providers.mailgun.enabled) {
      this.providers.set("mailgun", new MailgunProvider(providers.mailgun));
    }

    // Inicializar Resend
    if (providers.resend.enabled) {
      this.providers.set("resend", new ResendProvider(providers.resend));
    }

    // Inicializar SMTP
    if (providers.smtp.enabled) {
      this.providers.set("smtp", new SMTPProvider(providers.smtp));
    }

    console.log(
      `EMAIL SYSTEM > Initialized ${this.providers.size} providers:`,
      Array.from(this.providers.keys())
    );
  }

  /**
   * Seleciona o provedor apropriado para envio
   * @param {Object} emailData - Dados do email
   * @returns {Object} Provedor selecionado
   */
  selectProvider(emailData) {
    const { smtp, integrationId } = emailData;

    // Se especificado um provedor específico
    if (smtp && this.providers.has(smtp)) {
      return this.providers.get(smtp);
    }

    // Usar provedor padrão
    if (this.providers.has(this.defaultProvider)) {
      return this.providers.get(this.defaultProvider);
    }

    // Fallback para primeiro provedor disponível
    const availableProviders = Array.from(this.providers.values()).sort(
      (a, b) => a.config.priority - b.config.priority
    );

    if (availableProviders.length > 0) {
      return availableProviders[0];
    }

    throw new Error("No email providers available");
  }

  /**
   * Envia um email através do provedor apropriado
   * @param {Object} emailData - Dados do email
   * @returns {Promise<Object>} Resultado do envio
   */
  async sendEmail(emailData) {
    const startTime = Date.now();
    const emailId = emailData.id || "unknown";

    try {
      // Validar dados básicos
      this.validateEmailData(emailData);

      // Selecionar provedor
      const provider = this.selectProvider(emailData);

      console.log(
        `EMAIL SYSTEM > Sending email ${emailId} via ${provider.name}`
      );

      // Preparar dados para o provedor
      const preparedData = this.prepareEmailData(emailData);

      // Enviar email com timeout
      const result = await this.sendWithTimeout(provider, preparedData);

      const duration = Date.now() - startTime;

      // Log de sucesso
      this.logEmailOperation({
        level: "INFO",
        operation: "sendEmail",
        emailId,
        provider: provider.name,
        success: true,
        duration,
        result,
      });

      return {
        success: true,
        provider: provider.name,
        trackId: result.trackId || result.id,
        duration,
        result,
      };
    } catch (error) {
      const duration = Date.now() - startTime;

      // Log de erro
      this.logEmailOperation({
        level: "ERROR",
        operation: "sendEmail",
        emailId,
        success: false,
        duration,
        error: error.message,
      });

      // Determinar se é erro temporário
      const isTemporary = this.isTemporaryError(error);

      return {
        success: false,
        error: error.message,
        temporary: isTemporary,
        duration,
      };
    }
  }

  /**
   * Envia email com timeout
   * @param {Object} provider - Provedor de email
   * @param {Object} emailData - Dados do email
   * @returns {Promise<Object>} Resultado do envio
   */
  async sendWithTimeout(provider, emailData) {
    const timeout = EMAIL_CONFIG.timeouts.sendEmail;

    return Promise.race([
      provider.sendEmail(emailData),
      new Promise((_, reject) =>
        setTimeout(() => reject(new Error("Email send timeout")), timeout)
      ),
    ]);
  }

  /**
   * Valida dados básicos do email
   * @param {Object} emailData - Dados do email
   */
  validateEmailData(emailData) {
    const required = ["to", "subject", "html"];

    for (const field of required) {
      if (!emailData[field]) {
        throw new Error(`Missing required field: ${field}`);
      }
    }

    // Validar formato de email
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const recipients = emailData.to.split(",").map((email) => email.trim());

    for (const email of recipients) {
      if (!emailRegex.test(email)) {
        throw new Error(`Invalid email format: ${email}`);
      }
    }
  }

  /**
   * Prepara dados do email para envio
   * @param {Object} emailData - Dados originais
   * @returns {Object} Dados preparados
   */
  prepareEmailData(emailData) {
    return {
      to: emailData.to.split(",").map((email) => email.trim()),
      cc: emailData.cc
        ? emailData.cc.split(",").map((email) => email.trim())
        : undefined,
      bcc: emailData.bcc
        ? emailData.bcc.split(",").map((email) => email.trim())
        : undefined,
      from: emailData.from || "<EMAIL>",
      fromName: emailData.fromName || "QiPlus",
      subject: emailData.subject,
      html: emailData.html,
      text: emailData.text,
      attachments: emailData.attachments || [],
      tags: emailData.tags || [],
      metadata: {
        emailId: emailData.id,
        accountId: emailData.accountId,
        triggerId: emailData.triggerId,
      },
    };
  }

  /**
   * Determina se um erro é temporário
   * @param {Error} error - Erro ocorrido
   * @returns {boolean} True se for erro temporário
   */
  isTemporaryError(error) {
    const temporaryMessages = [
      "timeout",
      "rate limit",
      "server error",
      "service unavailable",
      "connection",
      "network",
    ];

    const message = error.message.toLowerCase();
    return temporaryMessages.some((msg) => message.includes(msg));
  }

  /**
   * Registra operação de email
   * @param {Object} logData - Dados do log
   */
  logEmailOperation(logData) {
    const logEntry = {
      timestamp: momentNow().format(CONSTANTS.MOMENT_ISO),
      component: "EmailSystem",
      ...logData,
    };

    console.log(`EMAIL SYSTEM > ${logData.level} >`, JSON.stringify(logEntry));
  }

  /**
   * Obtém estatísticas dos provedores
   * @returns {Object} Estatísticas
   */
  getProviderStats() {
    const stats = {};

    for (const [name, provider] of this.providers) {
      stats[name] = {
        enabled: true,
        priority: provider.config.priority,
        lastUsed: provider.lastUsed || null,
      };
    }

    return stats;
  }
}

// Instância singleton
let emailSystemInstance = null;

/**
 * Obtém instância do sistema de email
 * @returns {EmailSystem} Instância do sistema
 */
function getEmailSystem() {
  if (!emailSystemInstance) {
    emailSystemInstance = new EmailSystem();
  }
  return emailSystemInstance;
}

/**
 * Função principal para envio de email
 * @param {Object} emailData - Dados do email
 * @returns {Promise<Object>} Resultado do envio
 */
async function sendEmail(emailData) {
  const emailSystem = getEmailSystem();
  return emailSystem.sendEmail(emailData);
}

/**
 * Valida configuração de um provedor
 * @param {string} providerName - Nome do provedor
 * @returns {Promise<boolean>} True se configuração válida
 */
async function validateProviderConfig(providerName) {
  const emailSystem = getEmailSystem();
  const provider = emailSystem.providers.get(providerName);

  if (!provider) {
    return false;
  }

  try {
    return await provider.validateConfig();
  } catch (error) {
    console.error(
      `EMAIL SYSTEM > Provider ${providerName} validation failed:`,
      error.message
    );
    return false;
  }
}

module.exports = {
  EmailSystem,
  getEmailSystem,
  sendEmail,
  validateProviderConfig,
  EMAIL_CONFIG,
};

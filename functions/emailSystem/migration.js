/**
 * Sistema de Migração - Desativação do Sistema Antigo
 *
 * Este módulo gerencia a migração do sistema antigo de emails
 * para o novo sistema baseado em filas, fornecendo compatibilidade
 * e redirecionamento transparente.
 */

const { getEmailLogger } = require("./logger");
const { emailPrepare } = require("../emailPrepare");
const { sendEmail } = require("./index");

/**
 * Configurações de migração
 */
const MIGRATION_CONFIG = {
  // Controle de migração
  migration: {
    enabled: true,
    forceNewSystem: true, // Se true, força uso do novo sistema
    allowFallback: false, // Se true, permite fallback para sistema antigo em caso de erro
    logLegacyCalls: true, // Se true, loga todas as chamadas do sistema antigo
  },

  // Mapeamento de funções antigas para novas
  functionMapping: {
    sendMail: "sendEmailViaNewSystem",
    createBatchMailing: "createBatchEmailViaNewSystem",
    emailCron: "newEmailCron",
  },

  // Configurações de compatibilidade
  compatibility: {
    preserveOriginalSignatures: true,
    convertReturnFormats: true,
    maintainErrorHandling: true,
  },
};

/**
 * Wrapper para a função sendMail original
 * Redireciona para o novo sistema baseado em filas
 * @param {Object} data - Dados do email (formato antigo)
 * @param {Object} emailRef - Referência do documento (opcional)
 * @returns {Promise<Object>} Resultado no formato esperado pelo sistema antigo
 */
const sendMailWrapper = async (data, emailRef = null) => {
  const logger = getEmailLogger();

  if (MIGRATION_CONFIG.migration.logLegacyCalls) {
    logger.info(
      "Legacy sendMail called - redirecting to new system",
      {
        emailId: data.mailId || "unknown",
        to: data.to,
        subject: data.subject,
        hasEmailRef: !!emailRef,
      },
      "Migration"
    );
  }

  try {
    // Se migração não está habilitada, usar sistema antigo
    if (!MIGRATION_CONFIG.migration.enabled) {
      return await callLegacySendMail(data, emailRef);
    }

    // Converter dados do formato antigo para o novo
    const convertedData = convertLegacyEmailData(data);

    // Se o email deve ser enviado imediatamente (não agendado)
    if (shouldSendImmediately(data)) {
      return await sendEmailImmediately(convertedData, emailRef);
    } else {
      // Agendar email para envio via sistema de filas
      return await scheduleEmailViaNewSystem(convertedData, emailRef);
    }
  } catch (error) {
    logger.error(
      "Error in sendMail wrapper",
      {
        error: error.message,
        emailId: data.mailId || "unknown",
      },
      "Migration"
    );

    // Se fallback está habilitado, tentar sistema antigo
    if (MIGRATION_CONFIG.migration.allowFallback) {
      logger.warn(
        "Falling back to legacy system",
        {
          emailId: data.mailId || "unknown",
          error: error.message,
        },
        "Migration"
      );

      return await callLegacySendMail(data, emailRef);
    }

    throw error;
  }
};

/**
 * Converte dados do formato antigo para o novo sistema
 * @param {Object} legacyData - Dados no formato antigo
 * @returns {Object} Dados no formato novo
 */
const convertLegacyEmailData = (legacyData) => {
  const {
    to,
    cc,
    bcc,
    from,
    fromName,
    subject,
    html,
    text,
    scheduled_date,
    context,
    owner,
    accountId,
    tags,
    mailId,
    triggerId,
    smtp,
    integrationId,
    emailVars,
    attachments,
  } = legacyData;

  return {
    // Dados básicos
    to: to || "",
    cc: cc || "",
    bcc: bcc || "",
    from: from || "<EMAIL>",
    fromName: fromName || "QiPlus",
    subject: subject || "",
    html: html || "",
    text: text || "",

    // Agendamento
    scheduled_date: scheduled_date || new Date().toISOString(),

    // Configurações do provedor
    smtp: smtp || "qiplus_smtp",
    integrationId: integrationId || "",

    // Metadados
    accountId: accountId || context.accountId || "",
    owner: owner || "",
    context: context || {},
    emailVars: emailVars || {},

    // Rastreamento
    tags: tags || [],
    triggerId: triggerId || mailId || "",
    mailId: mailId || "",

    // Anexos
    attachments: attachments || [],

    // Dados de migração
    _migrated: true,
    _originalFormat: "legacy",
    _migrationTimestamp: new Date().toISOString(),
  };
};

/**
 * Determina se email deve ser enviado imediatamente
 * @param {Object} data - Dados do email
 * @returns {boolean} True se deve enviar imediatamente
 */
const shouldSendImmediately = (data) => {
  // Se não tem data de agendamento ou é para agora
  if (!data.scheduled_date) return true;

  const scheduledTime = new Date(data.scheduled_date).getTime();
  const now = Date.now();

  // Se agendado para menos de 2 minutos no futuro, enviar imediatamente
  return scheduledTime - now < 120000;
};

/**
 * Envia email imediatamente via novo sistema
 * @param {Object} emailData - Dados do email
 * @param {Object} emailRef - Referência do documento
 * @returns {Promise<Object>} Resultado no formato antigo
 */
const sendEmailImmediately = async (emailData, emailRef) => {
  const logger = getEmailLogger();

  try {
    logger.info(
      "Sending email immediately via new system",
      {
        emailId: emailData.mailId,
        to: emailData.to,
      },
      "Migration"
    );

    // Enviar via sistema unificado
    const result = await sendEmail(emailData);

    if (result.success) {
      // Atualizar documento se fornecido
      if (emailRef) {
        await emailRef.update({
          trackId: result.trackId,
          errorMsg: "",
          error: false,
          sending: false,
          sent: true,
          sentDate: new Date().toISOString(),
          provider: result.provider,
          _sentViaNewSystem: true,
        });
      }

      // Retornar no formato esperado pelo sistema antigo
      return {
        status: "success",
        trackId: result.trackId,
        provider: result.provider,
        duration: result.duration,
      };
    } else {
      throw new Error(result.error || "Unknown error");
    }
  } catch (error) {
    logger.error(
      "Error sending email immediately",
      {
        error: error.message,
        emailId: emailData.mailId,
      },
      "Migration"
    );

    // Atualizar documento com erro se fornecido
    if (emailRef) {
      await emailRef.update({
        errorMsg: error.message,
        error: true,
        sending: false,
        sent: false,
        errorDate: new Date().toISOString(),
        _errorInNewSystem: true,
      });
    }

    throw error;
  }
};

/**
 * Agenda email via novo sistema de filas
 * @param {Object} emailData - Dados do email
 * @param {Object} emailRef - Referência do documento
 * @returns {Promise<Object>} Resultado no formato antigo
 */
const scheduleEmailViaNewSystem = async (emailData, emailRef) => {
  const logger = getEmailLogger();

  try {
    logger.info(
      "Scheduling email via new system",
      {
        emailId: emailData.mailId,
        scheduledDate: emailData.scheduled_date,
      },
      "Migration"
    );

    // Marcar como preparado para ser processado pelo emailPrepare
    if (emailRef) {
      await emailRef.update({
        prepared: false, // Será marcado como true pelo emailPrepare
        sending: false,
        sent: false,
        error: false,
        _scheduledViaNewSystem: true,
        _migrationTimestamp: new Date().toISOString(),
      });
    }

    // Retornar sucesso - o email será processado pelo cron
    return {
      status: "scheduled",
      message: "Email scheduled for processing via new system",
      scheduledDate: emailData.scheduled_date,
    };
  } catch (error) {
    logger.error(
      "Error scheduling email via new system",
      {
        error: error.message,
        emailId: emailData.mailId,
      },
      "Migration"
    );

    throw error;
  }
};

/**
 * Chama o sistema antigo como fallback
 * @param {Object} data - Dados do email
 * @param {Object} emailRef - Referência do documento
 * @returns {Promise<Object>} Resultado do sistema antigo
 */
const callLegacySendMail = async (data, emailRef) => {
  const logger = getEmailLogger();

  logger.warn(
    "Using legacy email system",
    {
      emailId: data.mailId || "unknown",
      reason: "fallback or migration disabled",
    },
    "Migration"
  );

  // Importar função original do sistema antigo
  const { sendMail: legacySendMail } = require("../mailing");

  return await legacySendMail(data, emailRef);
};

/**
 * Wrapper para createBatchMailing
 * @param {Object} data - Dados do batch
 * @param {Object} docRef - Referência do documento
 * @returns {Promise<Object>} Resultado da criação
 */
const createBatchMailingWrapper = async (data, docRef = null) => {
  const logger = getEmailLogger();

  if (MIGRATION_CONFIG.migration.logLegacyCalls) {
    logger.info(
      "Legacy createBatchMailing called - redirecting to new system",
      {
        accountId: data.accountId,
        hasDocRef: !!docRef,
      },
      "Migration"
    );
  }

  try {
    // Se migração não está habilitada, usar sistema antigo
    if (!MIGRATION_CONFIG.migration.enabled) {
      const { createBatchMailing } = require("../mailing");
      return await createBatchMailing(data, docRef);
    }

    // Para batch emails, criar múltiplos emails individuais
    return await createBatchEmailsViaNewSystem(data, docRef);
  } catch (error) {
    logger.error(
      "Error in createBatchMailing wrapper",
      {
        error: error.message,
        accountId: data.accountId,
      },
      "Migration"
    );

    // Fallback se habilitado
    if (MIGRATION_CONFIG.migration.allowFallback) {
      const { createBatchMailing } = require("../mailing");
      return await createBatchMailing(data, docRef);
    }

    throw error;
  }
};

/**
 * Cria batch de emails via novo sistema
 * @param {Object} data - Dados do batch
 * @param {Object} docRef - Referência do documento
 * @returns {Promise<Object>} Resultado da criação
 */
const createBatchEmailsViaNewSystem = async (data, docRef) => {
  const logger = getEmailLogger();

  logger.info(
    "Creating batch emails via new system",
    {
      accountId: data.accountId,
      recipientCount: data.to ? data.to.split(",").length : 0,
    },
    "Migration"
  );

  // Converter para formato do novo sistema
  const convertedData = convertLegacyEmailData(data);

  // Criar documento no Firestore para ser processado pelo emailPrepare
  const { addNewPost } = require("../post");
  const { COLLECTIONS } = require("../init");

  const emailDoc = {
    ...convertedData,
    prepared: false,
    sent: false,
    sending: false,
    error: false,
    date: new Date().toISOString(),
    _createdViaNewSystem: true,
  };

  const result = await addNewPost(COLLECTIONS.MAIL_COLLECTION_NAME, emailDoc);

  logger.info(
    "Batch email created successfully",
    {
      emailId: result.ID,
      accountId: data.accountId,
    },
    "Migration"
  );

  return result;
};

/**
 * Obtém estatísticas de migração
 * @returns {Promise<Object>} Estatísticas de uso dos sistemas
 */
const getMigrationStats = async () => {
  try {
    const { getRedisClient } = require("../utils/redisClient");
    const redisClient = await getRedisClient();

    if (!redisClient) {
      return { error: "Redis not available" };
    }

    const today = new Date().toISOString().split("T")[0];
    const statsKey = `email:migration_stats:${today}`;

    const stats = await redisClient.hGetAll(statsKey);

    return {
      date: today,
      newSystemCalls: parseInt(stats.new_system || 0),
      legacyCalls: parseInt(stats.legacy || 0),
      fallbackCalls: parseInt(stats.fallback || 0),
      migrationEnabled: MIGRATION_CONFIG.migration.enabled,
      forceNewSystem: MIGRATION_CONFIG.migration.forceNewSystem,
    };
  } catch (error) {
    return { error: error.message };
  }
};

/**
 * Atualiza estatísticas de migração
 * @param {string} type - Tipo de chamada (new_system, legacy, fallback)
 */
const updateMigrationStats = async (type) => {
  try {
    const { getRedisClient } = require("../utils/redisClient");
    const redisClient = await getRedisClient();

    if (!redisClient) return;

    const today = new Date().toISOString().split("T")[0];
    const statsKey = `email:migration_stats:${today}`;

    await redisClient.hIncrBy(statsKey, type, 1);
    await redisClient.expire(statsKey, 30 * 24 * 60 * 60); // 30 dias
  } catch (error) {
    console.error("Error updating migration stats:", error.message);
  }
};

module.exports = {
  sendMailWrapper,
  createBatchMailingWrapper,
  convertLegacyEmailData,
  getMigrationStats,
  updateMigrationStats,
  MIGRATION_CONFIG,
};

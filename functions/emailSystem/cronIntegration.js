/**
 * Integração do Sistema de Emails com Cron Jobs
 * 
 * Este módulo integra o novo sistema de emails baseado em filas
 * com os cron jobs existentes, seguindo o padrão de processamento
 * sequencial com tratamento de erros isolado.
 */

const { emailPrepare } = require("../emailPrepare");
const { emailSend } = require("../emailSend");
const { getEmailLogger, getPerformanceTracker } = require("./logger");
const { getDLQManager } = require("./retryManager");

/**
 * Configurações da integração com cron
 */
const CRON_INTEGRATION_CONFIG = {
  // Timeouts para cada fase (ms)
  timeouts: {
    prepare: 120000, // 2 minutos
    send: 300000,    // 5 minutos
    cleanup: 60000   // 1 minuto
  },
  
  // Configurações de processamento
  processing: {
    maxEmailsPerRun: 100,
    enableCleanup: true,
    cleanupInterval: 24 * 60 * 60 * 1000, // 24 horas
    lastCleanupKey: 'email:last_cleanup'
  },
  
  // Configurações de monitoramento
  monitoring: {
    enablePerformanceTracking: true,
    enableDetailedLogging: true,
    alertOnErrors: true
  }
};

/**
 * Função principal do cron de emails - substitui o emailCron original
 * @param {Object} options - Opções de execução
 * @returns {Promise<Object>} Resultado da execução
 */
const newEmailCron = async (options = {}) => {
  const logger = getEmailLogger();
  const performanceTracker = getPerformanceTracker();
  const cronId = `email_cron_${Date.now()}`;
  
  logger.info("Email cron started", { cronId }, "EmailCron");
  
  if (CRON_INTEGRATION_CONFIG.monitoring.enablePerformanceTracking) {
    performanceTracker.startOperation(cronId, "email_cron", { options });
  }
  
  const startTime = Date.now();
  const results = {
    success: true,
    phases: {
      prepare: { success: false, duration: 0, processed: 0 },
      send: { success: false, duration: 0, sent: 0, failed: 0, retried: 0 },
      cleanup: { success: false, duration: 0, cleaned: 0 }
    },
    totalDuration: 0,
    errors: []
  };
  
  try {
    // FASE 1: PREPARAÇÃO DE EMAILS
    logger.info("Starting email preparation phase", { cronId }, "EmailCron");
    
    const prepareResult = await executeWithTimeout(
      () => emailPrepare({
        batchSize: options.maxEmails || CRON_INTEGRATION_CONFIG.processing.maxEmailsPerRun,
        ...options.prepareOptions
      }),
      CRON_INTEGRATION_CONFIG.timeouts.prepare,
      "Email preparation timeout"
    );
    
    results.phases.prepare = {
      success: prepareResult.success,
      duration: prepareResult.duration,
      processed: prepareResult.processed || 0,
      failed: prepareResult.failed || 0
    };
    
    if (!prepareResult.success) {
      results.errors.push(`Prepare phase failed: ${prepareResult.error}`);
      logger.error("Email preparation failed", { 
        cronId, 
        error: prepareResult.error 
      }, "EmailCron");
    } else {
      logger.info("Email preparation completed", {
        cronId,
        processed: prepareResult.processed,
        duration: prepareResult.duration
      }, "EmailCron");
    }
    
    // FASE 2: ENVIO DE EMAILS
    logger.info("Starting email sending phase", { cronId }, "EmailCron");
    
    const sendResult = await executeWithTimeout(
      () => emailSend({
        batchSize: options.maxEmails || CRON_INTEGRATION_CONFIG.processing.maxEmailsPerRun,
        ...options.sendOptions
      }),
      CRON_INTEGRATION_CONFIG.timeouts.send,
      "Email sending timeout"
    );
    
    results.phases.send = {
      success: sendResult.success,
      duration: sendResult.duration,
      sent: sendResult.sent || 0,
      failed: sendResult.failed || 0,
      retried: sendResult.retried || 0
    };
    
    if (!sendResult.success) {
      results.errors.push(`Send phase failed: ${sendResult.error}`);
      logger.error("Email sending failed", { 
        cronId, 
        error: sendResult.error 
      }, "EmailCron");
    } else {
      logger.info("Email sending completed", {
        cronId,
        sent: sendResult.sent,
        failed: sendResult.failed,
        retried: sendResult.retried,
        duration: sendResult.duration
      }, "EmailCron");
    }
    
    // FASE 3: LIMPEZA (se habilitada e necessária)
    if (CRON_INTEGRATION_CONFIG.processing.enableCleanup && await shouldRunCleanup()) {
      logger.info("Starting cleanup phase", { cronId }, "EmailCron");
      
      const cleanupResult = await executeWithTimeout(
        () => runCleanup(),
        CRON_INTEGRATION_CONFIG.timeouts.cleanup,
        "Cleanup timeout"
      );
      
      results.phases.cleanup = {
        success: cleanupResult.success,
        duration: cleanupResult.duration,
        cleaned: cleanupResult.cleaned || 0
      };
      
      if (!cleanupResult.success) {
        results.errors.push(`Cleanup phase failed: ${cleanupResult.error}`);
        logger.warn("Cleanup failed", { 
          cronId, 
          error: cleanupResult.error 
        }, "EmailCron");
      } else {
        logger.info("Cleanup completed", {
          cronId,
          cleaned: cleanupResult.cleaned,
          duration: cleanupResult.duration
        }, "EmailCron");
      }
    }
    
    // Calcular duração total
    results.totalDuration = Date.now() - startTime;
    
    // Determinar sucesso geral
    results.success = results.phases.prepare.success && results.phases.send.success;
    
    // Log final
    const logLevel = results.success ? 'INFO' : 'ERROR';
    logger.log(logLevel, "Email cron completed", {
      cronId,
      success: results.success,
      totalDuration: results.totalDuration,
      prepared: results.phases.prepare.processed,
      sent: results.phases.send.sent,
      failed: results.phases.send.failed,
      retried: results.phases.send.retried,
      errors: results.errors.length
    }, "EmailCron");
    
    // Finalizar tracking de performance
    if (CRON_INTEGRATION_CONFIG.monitoring.enablePerformanceTracking) {
      performanceTracker.endOperation(cronId, results);
    }
    
    return results;
    
  } catch (error) {
    results.success = false;
    results.totalDuration = Date.now() - startTime;
    results.errors.push(error.message);
    
    logger.fatal("Email cron fatal error", {
      cronId,
      error: error.message,
      stack: error.stack,
      totalDuration: results.totalDuration
    }, "EmailCron");
    
    // Finalizar tracking de performance com erro
    if (CRON_INTEGRATION_CONFIG.monitoring.enablePerformanceTracking) {
      performanceTracker.endOperation(cronId, { 
        ...results, 
        success: false, 
        error: error.message 
      });
    }
    
    return results;
  }
};

/**
 * Executa função com timeout
 * @param {Function} fn - Função a executar
 * @param {number} timeout - Timeout em ms
 * @param {string} timeoutMessage - Mensagem de timeout
 * @returns {Promise<any>} Resultado da função
 */
const executeWithTimeout = async (fn, timeout, timeoutMessage) => {
  return Promise.race([
    fn(),
    new Promise((_, reject) => 
      setTimeout(() => reject(new Error(timeoutMessage)), timeout)
    )
  ]);
};

/**
 * Verifica se deve executar limpeza
 * @returns {Promise<boolean>} True se deve executar limpeza
 */
const shouldRunCleanup = async () => {
  try {
    const { getRedisClient } = require("../utils/redisClient");
    const redisClient = await getRedisClient();
    
    if (!redisClient) return false;
    
    const lastCleanup = await redisClient.get(CRON_INTEGRATION_CONFIG.processing.lastCleanupKey);
    
    if (!lastCleanup) return true;
    
    const lastCleanupTime = parseInt(lastCleanup);
    const now = Date.now();
    
    return (now - lastCleanupTime) >= CRON_INTEGRATION_CONFIG.processing.cleanupInterval;
    
  } catch (error) {
    console.error("Error checking cleanup schedule:", error.message);
    return false;
  }
};

/**
 * Executa limpeza do sistema
 * @returns {Promise<Object>} Resultado da limpeza
 */
const runCleanup = async () => {
  const startTime = Date.now();
  let cleaned = 0;
  
  try {
    const dlqManager = getDLQManager();
    
    // Limpar dead letter queue
    const dlqCleaned = await dlqManager.cleanup();
    cleaned += dlqCleaned;
    
    // Atualizar timestamp da última limpeza
    const { getRedisClient } = require("../utils/redisClient");
    const redisClient = await getRedisClient();
    
    if (redisClient) {
      await redisClient.set(
        CRON_INTEGRATION_CONFIG.processing.lastCleanupKey, 
        Date.now().toString()
      );
    }
    
    return {
      success: true,
      duration: Date.now() - startTime,
      cleaned
    };
    
  } catch (error) {
    return {
      success: false,
      duration: Date.now() - startTime,
      cleaned,
      error: error.message
    };
  }
};

/**
 * Função de compatibilidade com o sistema antigo
 * Mantém a mesma assinatura do emailCron original
 * @returns {Promise<Object>} Resultado da execução
 */
const emailCronCompatibility = async () => {
  const logger = getEmailLogger();
  
  logger.info("Email cron called via compatibility layer", {}, "EmailCron");
  
  try {
    const result = await newEmailCron();
    
    // Converter resultado para formato esperado pelo sistema antigo
    if (result.success) {
      return {
        status: 'success',
        prepared: result.phases.prepare.processed,
        sent: result.phases.send.sent,
        duration: result.totalDuration
      };
    } else {
      throw new Error(result.errors.join('; '));
    }
    
  } catch (error) {
    logger.error("Email cron compatibility layer error", {
      error: error.message
    }, "EmailCron");
    
    throw error;
  }
};

/**
 * Obtém estatísticas do sistema de emails para monitoramento
 * @returns {Promise<Object>} Estatísticas do sistema
 */
const getEmailSystemStats = async () => {
  try {
    const { getEmailPrepareStats } = require("../emailPrepare");
    const { getEmailSendStats } = require("../emailSend");
    const { getRetryManager } = require("./retryManager");
    
    const [prepareStats, sendStats, retryStats] = await Promise.all([
      getEmailPrepareStats(),
      getEmailSendStats(),
      getRetryManager().getRetryStats()
    ]);
    
    return {
      prepare: prepareStats,
      send: sendStats,
      retry: retryStats,
      timestamp: new Date().toISOString()
    };
    
  } catch (error) {
    return {
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
};

/**
 * Função para teste do sistema de emails
 * @param {Object} options - Opções de teste
 * @returns {Promise<Object>} Resultado do teste
 */
const testEmailSystem = async (options = {}) => {
  const logger = getEmailLogger();
  
  logger.info("Starting email system test", { options }, "EmailTest");
  
  try {
    const result = await newEmailCron({
      ...options,
      prepareOptions: { ...options.prepareOptions, dryRun: true },
      sendOptions: { ...options.sendOptions, simulateOnly: true }
    });
    
    logger.info("Email system test completed", { result }, "EmailTest");
    
    return result;
    
  } catch (error) {
    logger.error("Email system test failed", { 
      error: error.message 
    }, "EmailTest");
    
    throw error;
  }
};

module.exports = {
  newEmailCron,
  emailCronCompatibility,
  getEmailSystemStats,
  testEmailSystem,
  CRON_INTEGRATION_CONFIG
};

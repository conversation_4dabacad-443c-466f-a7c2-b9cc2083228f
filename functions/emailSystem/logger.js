/**
 * Sistema de Logs Estruturados para Emails
 * 
 * Este módulo fornece logging estruturado com níveis apropriados,
 * métricas de performance e rastreamento unificado independente do provedor.
 */

const { momentNow, CONSTANTS } = require("../init");
const { getRedisClient } = require("../utils/redisClient");

const { MOMENT_ISO } = CONSTANTS;

/**
 * Níveis de log disponíveis
 */
const LOG_LEVELS = {
  DEBUG: 0,
  INFO: 1,
  WARN: 2,
  ERROR: 3,
  FATAL: 4
};

/**
 * Configurações do logger
 */
const LOGGER_CONFIG = {
  // Nível mínimo de log (DEBUG = 0, INFO = 1, WARN = 2, ERROR = 3, FATAL = 4)
  minLevel: process.env.NODE_ENV === 'production' ? LOG_LEVELS.INFO : LOG_LEVELS.DEBUG,
  
  // Configurações de métricas
  metrics: {
    enabled: true,
    retentionDays: 90,
    aggregationInterval: 60000, // 1 minuto
    redisKeyPrefix: 'email:metrics'
  },
  
  // Configurações de performance tracking
  performance: {
    enabled: true,
    slowThreshold: 5000, // 5 segundos
    trackingKey: 'email:performance'
  },
  
  // Configurações de alertas
  alerts: {
    enabled: true,
    errorThreshold: 10, // 10 erros por minuto
    slowRequestThreshold: 5, // 5 requests lentos por minuto
    alertKey: 'email:alerts'
  }
};

/**
 * Classe principal do logger
 */
class EmailLogger {
  constructor() {
    this.config = LOGGER_CONFIG;
    this.metricsBuffer = new Map();
    this.performanceBuffer = new Map();
    
    // Inicializar flush periódico das métricas
    this.startMetricsFlush();
  }
  
  /**
   * Log de debug
   * @param {string} message - Mensagem
   * @param {Object} metadata - Metadados adicionais
   * @param {string} component - Componente que está logando
   */
  debug(message, metadata = {}, component = 'EmailSystem') {
    this.log('DEBUG', message, metadata, component);
  }
  
  /**
   * Log de informação
   * @param {string} message - Mensagem
   * @param {Object} metadata - Metadados adicionais
   * @param {string} component - Componente que está logando
   */
  info(message, metadata = {}, component = 'EmailSystem') {
    this.log('INFO', message, metadata, component);
  }
  
  /**
   * Log de warning
   * @param {string} message - Mensagem
   * @param {Object} metadata - Metadados adicionais
   * @param {string} component - Componente que está logando
   */
  warn(message, metadata = {}, component = 'EmailSystem') {
    this.log('WARN', message, metadata, component);
  }
  
  /**
   * Log de erro
   * @param {string} message - Mensagem
   * @param {Object} metadata - Metadados adicionais
   * @param {string} component - Componente que está logando
   */
  error(message, metadata = {}, component = 'EmailSystem') {
    this.log('ERROR', message, metadata, component);
    
    // Incrementar contador de erros para alertas
    this.incrementErrorCount();
  }
  
  /**
   * Log de erro fatal
   * @param {string} message - Mensagem
   * @param {Object} metadata - Metadados adicionais
   * @param {string} component - Componente que está logando
   */
  fatal(message, metadata = {}, component = 'EmailSystem') {
    this.log('FATAL', message, metadata, component);
    
    // Incrementar contador de erros para alertas
    this.incrementErrorCount();
  }
  
  /**
   * Método principal de logging
   * @param {string} level - Nível do log
   * @param {string} message - Mensagem
   * @param {Object} metadata - Metadados adicionais
   * @param {string} component - Componente que está logando
   */
  log(level, message, metadata = {}, component = 'EmailSystem') {
    const levelValue = LOG_LEVELS[level];
    
    // Verificar se deve logar baseado no nível mínimo
    if (levelValue < this.config.minLevel) {
      return;
    }
    
    // Criar entrada de log estruturada
    const logEntry = {
      timestamp: momentNow().format(MOMENT_ISO),
      level,
      component,
      message,
      ...this.sanitizeMetadata(metadata),
      
      // Adicionar contexto do sistema
      system: {
        nodeEnv: process.env.NODE_ENV || 'development',
        version: process.env.npm_package_version || '1.0.0'
      }
    };
    
    // Adicionar trace ID se disponível
    if (metadata.traceId || metadata.emailId) {
      logEntry.traceId = metadata.traceId || metadata.emailId;
    }
    
    // Output do log
    this.outputLog(logEntry);
    
    // Coletar métricas se habilitado
    if (this.config.metrics.enabled) {
      this.collectMetrics(level, component, metadata);
    }
  }
  
  /**
   * Sanitiza metadados removendo informações sensíveis
   * @param {Object} metadata - Metadados originais
   * @returns {Object} Metadados sanitizados
   */
  sanitizeMetadata(metadata) {
    const sanitized = { ...metadata };
    
    // Remover campos sensíveis
    const sensitiveFields = ['password', 'apiKey', 'token', 'secret', 'auth'];
    
    for (const field of sensitiveFields) {
      if (sanitized[field]) {
        sanitized[field] = '[REDACTED]';
      }
    }
    
    // Truncar campos muito longos
    const maxLength = 1000;
    for (const [key, value] of Object.entries(sanitized)) {
      if (typeof value === 'string' && value.length > maxLength) {
        sanitized[key] = value.substring(0, maxLength) + '... [TRUNCATED]';
      }
    }
    
    return sanitized;
  }
  
  /**
   * Faz output do log
   * @param {Object} logEntry - Entrada de log
   */
  outputLog(logEntry) {
    const logString = JSON.stringify(logEntry);
    
    // Output baseado no nível
    switch (logEntry.level) {
      case 'DEBUG':
      case 'INFO':
        console.log(`EMAIL SYSTEM > ${logEntry.level} >`, logString);
        break;
      case 'WARN':
        console.warn(`EMAIL SYSTEM > ${logEntry.level} >`, logString);
        break;
      case 'ERROR':
      case 'FATAL':
        console.error(`EMAIL SYSTEM > ${logEntry.level} >`, logString);
        break;
    }
  }
  
  /**
   * Coleta métricas para análise
   * @param {string} level - Nível do log
   * @param {string} component - Componente
   * @param {Object} metadata - Metadados
   */
  collectMetrics(level, component, metadata) {
    const minute = Math.floor(Date.now() / 60000); // Minuto atual
    const key = `${component}:${minute}`;
    
    if (!this.metricsBuffer.has(key)) {
      this.metricsBuffer.set(key, {
        component,
        minute,
        debug: 0,
        info: 0,
        warn: 0,
        error: 0,
        fatal: 0,
        operations: new Set()
      });
    }
    
    const metrics = this.metricsBuffer.get(key);
    metrics[level.toLowerCase()]++;
    
    // Rastrear operações únicas
    if (metadata.operation) {
      metrics.operations.add(metadata.operation);
    }
  }
  
  /**
   * Incrementa contador de erros para alertas
   */
  incrementErrorCount() {
    const minute = Math.floor(Date.now() / 60000);
    const key = `errors:${minute}`;
    
    if (!this.metricsBuffer.has(key)) {
      this.metricsBuffer.set(key, { count: 0, minute });
    }
    
    this.metricsBuffer.get(key).count++;
    
    // Verificar threshold de alerta
    if (this.metricsBuffer.get(key).count >= this.config.alerts.errorThreshold) {
      this.triggerAlert('HIGH_ERROR_RATE', {
        count: this.metricsBuffer.get(key).count,
        minute,
        threshold: this.config.alerts.errorThreshold
      });
    }
  }
  
  /**
   * Inicia flush periódico das métricas para Redis
   */
  startMetricsFlush() {
    setInterval(() => {
      this.flushMetrics();
    }, this.config.metrics.aggregationInterval);
  }
  
  /**
   * Faz flush das métricas para Redis
   */
  async flushMetrics() {
    if (this.metricsBuffer.size === 0) return;
    
    try {
      const redisClient = await getRedisClient();
      if (!redisClient) return;
      
      const currentMinute = Math.floor(Date.now() / 60000);
      const cutoffMinute = currentMinute - 2; // Flush métricas de 2 minutos atrás
      
      for (const [key, metrics] of this.metricsBuffer.entries()) {
        if (metrics.minute <= cutoffMinute) {
          await this.saveMetricsToRedis(redisClient, metrics);
          this.metricsBuffer.delete(key);
        }
      }
      
    } catch (error) {
      console.error("EMAIL LOGGER > Error flushing metrics:", error.message);
    }
  }
  
  /**
   * Salva métricas no Redis
   * @param {Object} redisClient - Cliente Redis
   * @param {Object} metrics - Métricas a salvar
   */
  async saveMetricsToRedis(redisClient, metrics) {
    try {
      const date = new Date(metrics.minute * 60000);
      const dateKey = date.toISOString().split('T')[0]; // YYYY-MM-DD
      const metricsKey = `${this.config.metrics.redisKeyPrefix}:${dateKey}:${metrics.component}`;
      
      // Salvar métricas agregadas
      const metricsData = {
        debug: metrics.debug,
        info: metrics.info,
        warn: metrics.warn,
        error: metrics.error,
        fatal: metrics.fatal,
        operations: metrics.operations.size,
        minute: metrics.minute,
        timestamp: date.toISOString()
      };
      
      await redisClient.hSet(metricsKey, metricsData);
      
      // Definir expiração
      const expirationSeconds = this.config.metrics.retentionDays * 24 * 60 * 60;
      await redisClient.expire(metricsKey, expirationSeconds);
      
    } catch (error) {
      console.error("EMAIL LOGGER > Error saving metrics to Redis:", error.message);
    }
  }
  
  /**
   * Dispara alerta
   * @param {string} type - Tipo do alerta
   * @param {Object} data - Dados do alerta
   */
  triggerAlert(type, data) {
    const alert = {
      type,
      timestamp: momentNow().format(MOMENT_ISO),
      data,
      severity: this.getAlertSeverity(type)
    };
    
    console.warn(`EMAIL SYSTEM > ALERT > ${type} >`, JSON.stringify(alert));
    
    // Aqui poderia integrar com sistemas de alerta externos
    // como Slack, PagerDuty, etc.
  }
  
  /**
   * Determina severidade do alerta
   * @param {string} type - Tipo do alerta
   * @returns {string} Severidade
   */
  getAlertSeverity(type) {
    const severityMap = {
      HIGH_ERROR_RATE: 'HIGH',
      SLOW_REQUESTS: 'MEDIUM',
      DLQ_THRESHOLD: 'MEDIUM',
      PROVIDER_DOWN: 'HIGH'
    };
    
    return severityMap[type] || 'LOW';
  }
  
  /**
   * Obtém métricas agregadas
   * @param {Object} options - Opções de consulta
   * @returns {Promise<Object>} Métricas agregadas
   */
  async getAggregatedMetrics(options = {}) {
    try {
      const { 
        component = '*', 
        startDate = momentNow().subtract(1, 'day').format('YYYY-MM-DD'),
        endDate = momentNow().format('YYYY-MM-DD')
      } = options;
      
      const redisClient = await getRedisClient();
      if (!redisClient) {
        return { error: 'Redis not available' };
      }
      
      const metrics = {};
      const currentDate = new Date(startDate);
      const endDateObj = new Date(endDate);
      
      while (currentDate <= endDateObj) {
        const dateKey = currentDate.toISOString().split('T')[0];
        const pattern = `${this.config.metrics.redisKeyPrefix}:${dateKey}:${component}`;
        
        const keys = await redisClient.keys(pattern);
        
        for (const key of keys) {
          const data = await redisClient.hGetAll(key);
          const componentName = key.split(':').pop();
          
          if (!metrics[componentName]) {
            metrics[componentName] = {
              debug: 0,
              info: 0,
              warn: 0,
              error: 0,
              fatal: 0,
              operations: 0
            };
          }
          
          // Agregar métricas
          metrics[componentName].debug += parseInt(data.debug || 0);
          metrics[componentName].info += parseInt(data.info || 0);
          metrics[componentName].warn += parseInt(data.warn || 0);
          metrics[componentName].error += parseInt(data.error || 0);
          metrics[componentName].fatal += parseInt(data.fatal || 0);
          metrics[componentName].operations += parseInt(data.operations || 0);
        }
        
        currentDate.setDate(currentDate.getDate() + 1);
      }
      
      return {
        period: { startDate, endDate },
        metrics,
        timestamp: momentNow().format(MOMENT_ISO)
      };
      
    } catch (error) {
      console.error("EMAIL LOGGER > Error getting aggregated metrics:", error.message);
      return { error: error.message };
    }
  }
}

/**
 * Classe para tracking de performance
 */
class PerformanceTracker {
  constructor(logger) {
    this.logger = logger;
    this.activeOperations = new Map();
  }
  
  /**
   * Inicia tracking de uma operação
   * @param {string} operationId - ID único da operação
   * @param {string} operationType - Tipo da operação
   * @param {Object} metadata - Metadados da operação
   */
  startOperation(operationId, operationType, metadata = {}) {
    this.activeOperations.set(operationId, {
      type: operationType,
      startTime: Date.now(),
      metadata
    });
    
    this.logger.debug(`Operation started: ${operationType}`, {
      operationId,
      operationType,
      ...metadata
    }, 'PerformanceTracker');
  }
  
  /**
   * Finaliza tracking de uma operação
   * @param {string} operationId - ID da operação
   * @param {Object} result - Resultado da operação
   */
  endOperation(operationId, result = {}) {
    const operation = this.activeOperations.get(operationId);
    
    if (!operation) {
      this.logger.warn(`Operation not found: ${operationId}`, {
        operationId
      }, 'PerformanceTracker');
      return;
    }
    
    const duration = Date.now() - operation.startTime;
    const isSlowOperation = duration > LOGGER_CONFIG.performance.slowThreshold;
    
    // Log da operação
    const logLevel = isSlowOperation ? 'WARN' : 'INFO';
    this.logger.log(logLevel, `Operation completed: ${operation.type}`, {
      operationId,
      operationType: operation.type,
      duration,
      slow: isSlowOperation,
      success: result.success !== false,
      ...operation.metadata,
      ...result
    }, 'PerformanceTracker');
    
    // Remover da lista de operações ativas
    this.activeOperations.delete(operationId);
    
    // Alertar se operação foi muito lenta
    if (isSlowOperation) {
      this.logger.triggerAlert('SLOW_REQUESTS', {
        operationId,
        operationType: operation.type,
        duration,
        threshold: LOGGER_CONFIG.performance.slowThreshold
      });
    }
  }
  
  /**
   * Obtém operações ativas
   * @returns {Array} Lista de operações ativas
   */
  getActiveOperations() {
    const now = Date.now();
    const operations = [];
    
    for (const [id, operation] of this.activeOperations.entries()) {
      operations.push({
        id,
        type: operation.type,
        duration: now - operation.startTime,
        metadata: operation.metadata
      });
    }
    
    return operations;
  }
}

// Instâncias singleton
let loggerInstance = null;
let performanceTrackerInstance = null;

/**
 * Obtém instância do logger
 * @returns {EmailLogger} Instância do logger
 */
function getEmailLogger() {
  if (!loggerInstance) {
    loggerInstance = new EmailLogger();
  }
  return loggerInstance;
}

/**
 * Obtém instância do performance tracker
 * @returns {PerformanceTracker} Instância do tracker
 */
function getPerformanceTracker() {
  if (!performanceTrackerInstance) {
    const logger = getEmailLogger();
    performanceTrackerInstance = new PerformanceTracker(logger);
  }
  return performanceTrackerInstance;
}

module.exports = {
  EmailLogger,
  PerformanceTracker,
  getEmailLogger,
  getPerformanceTracker,
  LOG_LEVELS,
  LOGGER_CONFIG
};

/**
 * Classe base para provedores de email
 *
 * Define a interface comum que todos os provedores devem implementar
 */

const { momentNow, CONSTANTS } = require("../../init");

class BaseProvider {
  constructor(config) {
    this.config = config;
    this.name = "base";
    this.lastUsed = null;
    this.stats = {
      sent: 0,
      failed: 0,
      totalTime: 0,
    };
  }

  /**
   * Envia um email - deve ser implementado pelas subclasses
   * @param {Object} emailData - Dados do email
   * @returns {Promise<Object>} Resultado do envio
   */
  async sendEmail(emailData) {
    throw new Error(`sendEmail must be implemented by ${this.name} provider`);
  }

  /**
   * Valida a configuração do provedor - deve ser implementado pelas subclasses
   * @returns {Promise<boolean>} True se configuração válida
   */
  async validateConfig() {
    throw new Error(
      `validateConfig must be implemented by ${this.name} provider`
    );
  }

  /**
   * Determina se um erro é temporário - pode ser sobrescrito pelas subclasses
   * @param {Error} error - Erro ocorrido
   * @returns {boolean} True se for erro temporário
   */
  isTemporaryError(error) {
    // Implementação base - detecta erros temporários comuns
    const temporaryPatterns = [
      /timeout/i,
      /rate.?limit/i,
      /server.?error/i,
      /service.?unavailable/i,
      /connection/i,
      /network/i,
      /429/,
      /5\d\d/, // Códigos 5xx
    ];

    const message = error.message || "";
    const code = error.code || error.status || "";

    return temporaryPatterns.some(
      (pattern) => pattern.test(message) || pattern.test(String(code))
    );
  }

  /**
   * Formata erro de forma padronizada
   * @param {Error} error - Erro original
   * @returns {Object} Erro formatado
   */
  formatError(error) {
    return {
      message: error.message || "Unknown error",
      code: error.code || error.status || "UNKNOWN",
      temporary: this.isTemporaryError(error),
      provider: this.name,
      timestamp: momentNow().format(CONSTANTS.MOMENT_ISO),
    };
  }

  /**
   * Valida dados básicos do email
   * @param {Object} emailData - Dados do email
   */
  validateEmailData(emailData) {
    const required = ["to", "subject", "html"];

    for (const field of required) {
      if (!emailData[field]) {
        throw new Error(`Missing required field: ${field}`);
      }
    }

    // Validar array de destinatários
    if (!Array.isArray(emailData.to) || emailData.to.length === 0) {
      throw new Error("Recipients (to) must be a non-empty array");
    }

    // Validar formato de emails
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

    for (const email of emailData.to) {
      if (!emailRegex.test(email)) {
        throw new Error(`Invalid email format: ${email}`);
      }
    }

    // Validar CC se presente
    if (emailData.cc && Array.isArray(emailData.cc)) {
      for (const email of emailData.cc) {
        if (!emailRegex.test(email)) {
          throw new Error(`Invalid CC email format: ${email}`);
        }
      }
    }

    // Validar BCC se presente
    if (emailData.bcc && Array.isArray(emailData.bcc)) {
      for (const email of emailData.bcc) {
        if (!emailRegex.test(email)) {
          throw new Error(`Invalid BCC email format: ${email}`);
        }
      }
    }
  }

  /**
   * Prepara anexos para o formato do provedor
   * @param {Array} attachments - Lista de anexos
   * @returns {Array} Anexos formatados
   */
  prepareAttachments(attachments) {
    if (!attachments || !Array.isArray(attachments)) {
      return [];
    }

    return attachments.map((attachment) => {
      if (!attachment.filename || !attachment.content) {
        throw new Error("Attachment must have filename and content");
      }

      return {
        filename: attachment.filename,
        content: attachment.content,
        contentType: attachment.contentType || "application/octet-stream",
        encoding: attachment.encoding || "base64",
      };
    });
  }

  /**
   * Atualiza estatísticas do provedor
   * @param {boolean} success - Se o envio foi bem-sucedido
   * @param {number} duration - Duração em ms
   */
  updateStats(success, duration) {
    this.lastUsed = momentNow().format(CONSTANTS.MOMENT_ISO);
    this.stats.totalTime += duration;

    if (success) {
      this.stats.sent++;
    } else {
      this.stats.failed++;
    }
  }

  /**
   * Obtém estatísticas do provedor
   * @returns {Object} Estatísticas
   */
  getStats() {
    const total = this.stats.sent + this.stats.failed;
    const avgTime = total > 0 ? Math.round(this.stats.totalTime / total) : 0;
    const successRate = total > 0 ? this.stats.sent / total : 0;

    return {
      name: this.name,
      sent: this.stats.sent,
      failed: this.stats.failed,
      total,
      successRate: Math.round(successRate * 100) / 100,
      avgTime,
      lastUsed: this.lastUsed,
    };
  }

  /**
   * Registra log específico do provedor
   * @param {string} level - Nível do log
   * @param {string} message - Mensagem
   * @param {Object} metadata - Metadados adicionais
   */
  log(level, message, metadata = {}) {
    const logEntry = {
      timestamp: momentNow().format(CONSTANTS.MOMENT_ISO),
      level: level.toUpperCase(),
      provider: this.name,
      message,
      ...metadata,
    };

    console.log(
      `EMAIL PROVIDER > ${this.name.toUpperCase()} > ${level.toUpperCase()} >`,
      JSON.stringify(logEntry)
    );
  }

  /**
   * Prepara headers customizados
   * @param {Object} emailData - Dados do email
   * @returns {Object} Headers preparados
   */
  prepareHeaders(emailData) {
    const headers = {};

    // Adicionar ID de rastreamento se disponível
    if (emailData.metadata.emailId) {
      headers["X-Email-ID"] = emailData.metadata.emailId;
    }

    // Adicionar ID da conta
    if (emailData.metadata.accountId) {
      headers["X-Account-ID"] = emailData.metadata.accountId;
    }

    // Adicionar ID do trigger
    if (emailData.metadata.triggerId) {
      headers["X-Trigger-ID"] = emailData.metadata.triggerId;
    }

    // Adicionar timestamp
    headers["X-Sent-At"] = momentNow().format(CONSTANTS.MOMENT_ISO);

    return headers;
  }

  /**
   * Sanitiza conteúdo HTML
   * @param {string} html - Conteúdo HTML
   * @returns {string} HTML sanitizado
   */
  sanitizeHtml(html) {
    if (!html) return "";

    // Remover scripts maliciosos (implementação básica)
    return html.replace(
      /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
      ""
    );
  }

  /**
   * Gera versão texto a partir do HTML se não fornecida
   * @param {string} html - Conteúdo HTML
   * @param {string} text - Texto existente
   * @returns {string} Versão texto
   */
  generateTextVersion(html, text) {
    if (text) return text;

    // Implementação básica - remover tags HTML
    return html
      .replace(/<[^>]*>/g, "")
      .replace(/\s+/g, " ")
      .trim();
  }
}

module.exports = BaseProvider;

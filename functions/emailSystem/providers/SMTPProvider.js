/**
 * Provedor SMTP para o sistema unificado de email
 */

const BaseProvider = require("./BaseProvider");
const nodemailer = require("nodemailer");

class SMTPProvider extends BaseProvider {
  constructor(config) {
    super(config);
    this.name = "smtp";
    this.transporter = null;

    // Inicializar transporter SMTP
    this.initializeTransporter();

    this.log("INFO", "SMTP provider initialized", {
      host: config.host,
      port: config.port,
      secure: config.secure,
    });
  }

  /**
   * Inicializa o transporter do nodemailer
   */
  initializeTransporter() {
    const transportConfig = {
      host: this.config.host,
      port: this.config.port,
      secure: this.config.secure || false,
      auth: {
        user: this.config.auth.user,
        pass: this.config.auth.pass,
      },
      // Configurações adicionais para melhor compatibilidade
      tls: {
        rejectUnauthorized: false,
      },
      connectionTimeout: 60000, // 60 segundos
      greetingTimeout: 30000, // 30 segundos
      socketTimeout: 60000, // 60 segundos
    };

    this.transporter = nodemailer.createTransporter(transportConfig);
  }

  /**
   * Envia email através do SMTP
   * @param {Object} emailData - Dados do email
   * @returns {Promise<Object>} Resultado do envio
   */
  async sendEmail(emailData) {
    const startTime = Date.now();

    try {
      // Validar dados
      this.validateEmailData(emailData);

      // Preparar dados para SMTP
      const smtpData = this.prepareSMTPData(emailData);

      this.log("DEBUG", "Sending email via SMTP", {
        to: emailData.to,
        subject: emailData.subject,
        hasAttachments: (emailData.attachments || []).length > 0,
      });

      // Enviar email
      const result = await this.transporter.sendMail(smtpData);

      const duration = Date.now() - startTime;
      this.updateStats(true, duration);

      this.log("INFO", "Email sent successfully via SMTP", {
        messageId: result.messageId,
        duration,
      });

      return {
        success: true,
        trackId: result.messageId,
        messageId: result.messageId,
        provider: this.name,
        duration,
        response: result.response,
      };
    } catch (error) {
      const duration = Date.now() - startTime;
      this.updateStats(false, duration);

      const formattedError = this.formatError(error);

      this.log("ERROR", "Failed to send email via SMTP", {
        error: formattedError,
        duration,
      });

      throw error;
    }
  }

  /**
   * Prepara dados no formato do nodemailer/SMTP
   * @param {Object} emailData - Dados originais
   * @returns {Object} Dados formatados para SMTP
   */
  prepareSMTPData(emailData) {
    const data = {
      from: this.formatFromAddress(emailData.from, emailData.fromName),
      to: emailData.to.join(", "),
      subject: emailData.subject,
      html: this.sanitizeHtml(emailData.html),
    };

    // Adicionar CC se presente
    if (emailData.cc && emailData.cc.length > 0) {
      data.cc = emailData.cc.join(", ");
    }

    // Adicionar BCC se presente
    if (emailData.bcc && emailData.bcc.length > 0) {
      data.bcc = emailData.bcc.join(", ");
    }

    // Adicionar versão texto
    if (emailData.text) {
      data.text = emailData.text;
    } else {
      data.text = this.generateTextVersion(emailData.html);
    }

    // Adicionar reply-to se configurado
    if (emailData.replyTo) {
      data.replyTo = emailData.replyTo;
    }

    // Adicionar headers customizados
    const headers = this.prepareHeaders(emailData);
    if (Object.keys(headers).length > 0) {
      data.headers = headers;
    }

    // Adicionar anexos se presentes
    if (emailData.attachments && emailData.attachments.length > 0) {
      data.attachments = this.prepareSMTPAttachments(emailData.attachments);
    }

    // Configurações adicionais do nodemailer
    data.encoding = "utf8";
    data.textEncoding = "base64";

    return data;
  }

  /**
   * Formata endereço de origem para SMTP
   * @param {string} from - Email de origem
   * @param {string} fromName - Nome de origem
   * @returns {string} Endereço formatado
   */
  formatFromAddress(from, fromName) {
    if (fromName) {
      return `"${fromName}" <${from}>`;
    }
    return from;
  }

  /**
   * Prepara anexos para o formato nodemailer/SMTP
   * @param {Array} attachments - Lista de anexos
   * @returns {Array} Anexos formatados para SMTP
   */
  prepareSMTPAttachments(attachments) {
    return attachments.map((attachment) => {
      const smtpAttachment = {
        filename: attachment.filename,
        contentType: attachment.contentType || "application/octet-stream",
      };

      // Nodemailer aceita content como buffer ou base64
      if (attachment.encoding === "base64") {
        smtpAttachment.content = attachment.content;
        smtpAttachment.encoding = "base64";
      } else if (Buffer.isBuffer(attachment.content)) {
        smtpAttachment.content = attachment.content;
      } else {
        smtpAttachment.content = Buffer.from(attachment.content);
      }

      return smtpAttachment;
    });
  }

  /**
   * Determina se um erro SMTP é temporário
   * @param {Error} error - Erro SMTP
   * @returns {boolean} True se for erro temporário
   */
  isTemporaryError(error) {
    // Códigos de resposta SMTP temporários
    const temporaryCodes = [
      421, // Service not available
      450, // Requested mail action not taken: mailbox unavailable
      451, // Requested action aborted: local error in processing
      452, // Requested action not taken: insufficient system storage
      454, // Temporary authentication failure
    ];

    const code = error.responseCode || error.code;

    if (temporaryCodes.includes(code)) {
      return true;
    }

    // Verificar mensagens específicas do SMTP
    const message = (error.message || "").toLowerCase();
    const temporaryMessages = [
      "timeout",
      "connection",
      "network",
      "temporary",
      "try again",
      "rate limit",
      "too many",
      "server busy",
      "service unavailable",
    ];

    if (temporaryMessages.some((msg) => message.includes(msg))) {
      return true;
    }

    // Usar implementação base para outros casos
    return super.isTemporaryError(error);
  }

  /**
   * Valida configuração do SMTP
   * @returns {Promise<boolean>} True se configuração válida
   */
  async validateConfig() {
    try {
      if (!this.config.host) {
        throw new Error("SMTP host is required");
      }

      if (!this.config.port) {
        throw new Error("SMTP port is required");
      }

      if (
        !this.config.auth ||
        !this.config.auth.user ||
        !this.config.auth.pass
      ) {
        throw new Error("SMTP authentication credentials are required");
      }

      // Testar conexão SMTP
      await this.transporter.verify();

      this.log("INFO", "SMTP configuration validated successfully");
      return true;
    } catch (error) {
      this.log("ERROR", "SMTP configuration validation failed", {
        error: error.message,
      });
      return false;
    }
  }

  /**
   * Obtém estatísticas específicas do SMTP
   * @returns {Object} Estatísticas estendidas
   */
  getStats() {
    const baseStats = super.getStats();

    return {
      ...baseStats,
      host: this.config.host,
      port: this.config.port,
      secure: this.config.secure,
      authConfigured: !!(this.config.auth.user && this.config.auth.pass),
    };
  }

  /**
   * Testa conexão SMTP
   * @returns {Promise<boolean>} True se conexão bem-sucedida
   */
  async testConnection() {
    try {
      const isConnected = await this.transporter.verify();

      this.log("INFO", "SMTP connection test successful");
      return isConnected;
    } catch (error) {
      this.log("ERROR", "SMTP connection test failed", {
        error: error.message,
      });
      return false;
    }
  }

  /**
   * Fecha conexão SMTP
   */
  async close() {
    if (this.transporter) {
      this.transporter.close();
      this.log("INFO", "SMTP connection closed");
    }
  }

  /**
   * Reconecta o transporter SMTP
   */
  async reconnect() {
    try {
      await this.close();
      this.initializeTransporter();
      await this.testConnection();

      this.log("INFO", "SMTP reconnection successful");
      return true;
    } catch (error) {
      this.log("ERROR", "SMTP reconnection failed", {
        error: error.message,
      });
      return false;
    }
  }
}

module.exports = SMTPProvider;

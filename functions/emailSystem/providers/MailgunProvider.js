/**
 * Provedor Mailgun para o sistema unificado de email
 */

const BaseProvider = require('./BaseProvider');
const mailgun = require('mailgun-js');

class MailgunProvider extends BaseProvider {
  constructor(config) {
    super(config);
    this.name = 'mailgun';
    
    // Inicializar cliente Mailgun
    this.client = mailgun({
      apiKey: config.apiKey,
      domain: config.domain,
      host: config.host || 'api.mailgun.net'
    });
    
    this.log('INFO', 'Mailgun provider initialized', {
      domain: config.domain,
      host: config.host || 'api.mailgun.net'
    });
  }
  
  /**
   * Envia email através do Mailgun
   * @param {Object} emailData - Dados do email
   * @returns {Promise<Object>} Resultado do envio
   */
  async sendEmail(emailData) {
    const startTime = Date.now();
    
    try {
      // Validar dados
      this.validateEmailData(emailData);
      
      // Preparar dados para Mailgun
      const mailgunData = this.prepareMailgunData(emailData);
      
      this.log('DEBUG', 'Sending email via Mailgun', {
        to: emailData.to,
        subject: emailData.subject,
        hasAttachments: (emailData.attachments || []).length > 0
      });
      
      // Enviar email
      const result = await this.client.messages().send(mailgunData);
      
      const duration = Date.now() - startTime;
      this.updateStats(true, duration);
      
      this.log('INFO', 'Email sent successfully via Mailgun', {
        messageId: result.id,
        duration
      });
      
      return {
        success: true,
        trackId: result.id,
        messageId: result.id,
        provider: this.name,
        duration
      };
      
    } catch (error) {
      const duration = Date.now() - startTime;
      this.updateStats(false, duration);
      
      const formattedError = this.formatError(error);
      
      this.log('ERROR', 'Failed to send email via Mailgun', {
        error: formattedError,
        duration
      });
      
      throw error;
    }
  }
  
  /**
   * Prepara dados no formato do Mailgun
   * @param {Object} emailData - Dados originais
   * @returns {Object} Dados formatados para Mailgun
   */
  prepareMailgunData(emailData) {
    const data = {
      from: this.formatFromAddress(emailData.from, emailData.fromName),
      to: emailData.to.join(', '),
      subject: emailData.subject,
      html: this.sanitizeHtml(emailData.html)
    };
    
    // Adicionar CC se presente
    if (emailData.cc && emailData.cc.length > 0) {
      data.cc = emailData.cc.join(', ');
    }
    
    // Adicionar BCC se presente
    if (emailData.bcc && emailData.bcc.length > 0) {
      data.bcc = emailData.bcc.join(', ');
    }
    
    // Adicionar versão texto
    if (emailData.text) {
      data.text = emailData.text;
    } else {
      data.text = this.generateTextVersion(emailData.html);
    }
    
    // Adicionar tags se presentes
    if (emailData.tags && emailData.tags.length > 0) {
      data['o:tag'] = emailData.tags;
    }
    
    // Adicionar headers customizados
    const headers = this.prepareHeaders(emailData);
    for (const [key, value] of Object.entries(headers)) {
      data[`h:${key}`] = value;
    }
    
    // Adicionar variáveis customizadas para tracking
    if (emailData.metadata) {
      data['v:email-id'] = emailData.metadata.emailId;
      data['v:account-id'] = emailData.metadata.accountId;
      data['v:trigger-id'] = emailData.metadata.triggerId;
    }
    
    // Adicionar anexos se presentes
    if (emailData.attachments && emailData.attachments.length > 0) {
      data.attachment = this.prepareMailgunAttachments(emailData.attachments);
    }
    
    return data;
  }
  
  /**
   * Formata endereço de origem
   * @param {string} from - Email de origem
   * @param {string} fromName - Nome de origem
   * @returns {string} Endereço formatado
   */
  formatFromAddress(from, fromName) {
    if (fromName) {
      return `${fromName} <${from}>`;
    }
    return from;
  }
  
  /**
   * Prepara anexos para o formato Mailgun
   * @param {Array} attachments - Lista de anexos
   * @returns {Array} Anexos formatados para Mailgun
   */
  prepareMailgunAttachments(attachments) {
    return attachments.map(attachment => {
      // Mailgun espera um buffer ou stream
      let content;
      
      if (attachment.encoding === 'base64') {
        content = Buffer.from(attachment.content, 'base64');
      } else {
        content = attachment.content;
      }
      
      return {
        data: content,
        filename: attachment.filename,
        contentType: attachment.contentType
      };
    });
  }
  
  /**
   * Determina se um erro do Mailgun é temporário
   * @param {Error} error - Erro do Mailgun
   * @returns {boolean} True se for erro temporário
   */
  isTemporaryError(error) {
    // Códigos de erro temporário específicos do Mailgun
    const temporaryCodes = [
      429, // Rate limit
      500, // Internal server error
      502, // Bad gateway
      503, // Service unavailable
      504  // Gateway timeout
    ];
    
    const status = error.status || error.statusCode;
    
    if (temporaryCodes.includes(status)) {
      return true;
    }
    
    // Verificar mensagens específicas do Mailgun
    const message = (error.message || '').toLowerCase();
    const temporaryMessages = [
      'rate limit',
      'too many requests',
      'server error',
      'service unavailable',
      'timeout'
    ];
    
    if (temporaryMessages.some(msg => message.includes(msg))) {
      return true;
    }
    
    // Usar implementação base para outros casos
    return super.isTemporaryError(error);
  }
  
  /**
   * Valida configuração do Mailgun
   * @returns {Promise<boolean>} True se configuração válida
   */
  async validateConfig() {
    try {
      if (!this.config.apiKey) {
        throw new Error('Mailgun API key is required');
      }
      
      if (!this.config.domain) {
        throw new Error('Mailgun domain is required');
      }
      
      // Testar conexão fazendo uma consulta simples
      await this.client.domains(this.config.domain).info();
      
      this.log('INFO', 'Mailgun configuration validated successfully');
      return true;
      
    } catch (error) {
      this.log('ERROR', 'Mailgun configuration validation failed', {
        error: error.message
      });
      return false;
    }
  }
  
  /**
   * Obtém estatísticas específicas do Mailgun
   * @returns {Object} Estatísticas estendidas
   */
  getStats() {
    const baseStats = super.getStats();
    
    return {
      ...baseStats,
      domain: this.config.domain,
      apiKeyConfigured: !!this.config.apiKey
    };
  }
  
  /**
   * Obtém logs de eventos do Mailgun
   * @param {Object} options - Opções de consulta
   * @returns {Promise<Array>} Lista de eventos
   */
  async getEvents(options = {}) {
    try {
      const events = await this.client.events().get(options);
      return events.items || [];
    } catch (error) {
      this.log('ERROR', 'Failed to fetch Mailgun events', {
        error: error.message
      });
      return [];
    }
  }
  
  /**
   * Verifica status de entrega de um email
   * @param {string} messageId - ID da mensagem
   * @returns {Promise<Object>} Status de entrega
   */
  async getDeliveryStatus(messageId) {
    try {
      const events = await this.getEvents({
        'message-id': messageId,
        limit: 10
      });
      
      const deliveryEvents = events.filter(event => 
        ['delivered', 'failed', 'rejected'].includes(event.event)
      );
      
      if (deliveryEvents.length > 0) {
        const latestEvent = deliveryEvents[0];
        return {
          status: latestEvent.event,
          timestamp: latestEvent.timestamp,
          reason: latestEvent.reason || null
        };
      }
      
      return {
        status: 'pending',
        timestamp: null,
        reason: null
      };
      
    } catch (error) {
      this.log('ERROR', 'Failed to get delivery status', {
        messageId,
        error: error.message
      });
      
      return {
        status: 'unknown',
        timestamp: null,
        reason: error.message
      };
    }
  }
}

module.exports = MailgunProvider;

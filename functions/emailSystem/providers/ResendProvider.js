/**
 * Provedor Resend para o sistema unificado de email
 */

const BaseProvider = require("./BaseProvider");
const { Resend } = require("resend");

class ResendProvider extends BaseProvider {
  constructor(config) {
    super(config);
    this.name = "resend";

    // Inicializar cliente Resend
    this.client = new Resend(config.apiKey);

    this.log("INFO", "Resend provider initialized", {
      domain: config.domain,
    });
  }

  /**
   * Envia email através do Resend
   * @param {Object} emailData - Dados do email
   * @returns {Promise<Object>} Resultado do envio
   */
  async sendEmail(emailData) {
    const startTime = Date.now();

    try {
      // Validar dados
      this.validateEmailData(emailData);

      // Preparar dados para Resend
      const resendData = this.prepareResendData(emailData);

      this.log("DEBUG", "Sending email via Resend", {
        to: emailData.to,
        subject: emailData.subject,
        hasAttachments: (emailData.attachments || []).length > 0,
      });

      // Enviar email
      const result = await this.client.emails.send(resendData);

      if (result.error) {
        throw new Error(result.error.message || "Resend API error");
      }

      const duration = Date.now() - startTime;
      this.updateStats(true, duration);

      this.log("INFO", "Email sent successfully via Resend", {
        messageId: result.data.id,
        duration,
      });

      return {
        success: true,
        trackId: result.data.id,
        messageId: result.data.id,
        provider: this.name,
        duration,
      };
    } catch (error) {
      const duration = Date.now() - startTime;
      this.updateStats(false, duration);

      const formattedError = this.formatError(error);

      this.log("ERROR", "Failed to send email via Resend", {
        error: formattedError,
        duration,
      });

      throw error;
    }
  }

  /**
   * Prepara dados no formato do Resend
   * @param {Object} emailData - Dados originais
   * @returns {Object} Dados formatados para Resend
   */
  prepareResendData(emailData) {
    const data = {
      from: this.formatFromAddress(emailData.from, emailData.fromName),
      to: emailData.to,
      subject: emailData.subject,
      html: this.sanitizeHtml(emailData.html),
    };

    // Adicionar CC se presente
    if (emailData.cc && emailData.cc.length > 0) {
      data.cc = emailData.cc;
    }

    // Adicionar BCC se presente
    if (emailData.bcc && emailData.bcc.length > 0) {
      data.bcc = emailData.bcc;
    }

    // Adicionar versão texto
    if (emailData.text) {
      data.text = emailData.text;
    } else {
      data.text = this.generateTextVersion(emailData.html);
    }

    // Adicionar tags se presentes
    if (emailData.tags && emailData.tags.length > 0) {
      data.tags = emailData.tags;
    }

    // Adicionar headers customizados
    const headers = this.prepareHeaders(emailData);
    if (Object.keys(headers).length > 0) {
      data.headers = headers;
    }

    // Adicionar anexos se presentes
    if (emailData.attachments && emailData.attachments.length > 0) {
      data.attachments = this.prepareResendAttachments(emailData.attachments);
    }

    // Adicionar reply-to se configurado
    if (emailData.replyTo) {
      data.reply_to = emailData.replyTo;
    }

    return data;
  }

  /**
   * Formata endereço de origem para Resend
   * @param {string} from - Email de origem
   * @param {string} fromName - Nome de origem
   * @returns {string} Endereço formatado
   */
  formatFromAddress(from, fromName) {
    if (fromName) {
      return `${fromName} <${from}>`;
    }
    return from;
  }

  /**
   * Prepara anexos para o formato Resend
   * @param {Array} attachments - Lista de anexos
   * @returns {Array} Anexos formatados para Resend
   */
  prepareResendAttachments(attachments) {
    return attachments.map((attachment) => {
      const resendAttachment = {
        filename: attachment.filename,
        content: attachment.content,
      };

      // Resend aceita content como base64 string ou buffer
      if (attachment.encoding === "base64") {
        resendAttachment.content = attachment.content;
      } else if (Buffer.isBuffer(attachment.content)) {
        resendAttachment.content = attachment.content.toString("base64");
      }

      // Adicionar content type se disponível
      if (attachment.contentType) {
        resendAttachment.type = attachment.contentType;
      }

      return resendAttachment;
    });
  }

  /**
   * Determina se um erro do Resend é temporário
   * @param {Error} error - Erro do Resend
   * @returns {boolean} True se for erro temporário
   */
  isTemporaryError(error) {
    // Verificar tipos de erro específicos do Resend
    const errorName = error.name || "";
    const temporaryErrorNames = [
      "RateLimitError",
      "InternalServerError",
      "ServiceUnavailableError",
      "TimeoutError",
    ];

    if (temporaryErrorNames.includes(errorName)) {
      return true;
    }

    // Verificar códigos de status HTTP
    const status = error.status || error.statusCode;
    const temporaryCodes = [429, 500, 502, 503, 504];

    if (temporaryCodes.includes(status)) {
      return true;
    }

    // Verificar mensagens específicas
    const message = (error.message || "").toLowerCase();
    const temporaryMessages = [
      "rate limit",
      "too many requests",
      "server error",
      "service unavailable",
      "timeout",
      "network error",
    ];

    if (temporaryMessages.some((msg) => message.includes(msg))) {
      return true;
    }

    // Usar implementação base para outros casos
    return super.isTemporaryError(error);
  }

  /**
   * Valida configuração do Resend
   * @returns {Promise<boolean>} True se configuração válida
   */
  async validateConfig() {
    try {
      if (!this.config.apiKey) {
        throw new Error("Resend API key is required");
      }

      // Testar conexão fazendo uma consulta simples
      const domains = await this.client.domains.list();

      this.log("INFO", "Resend configuration validated successfully", {
        domainsCount: domains.data.length || 0,
      });

      return true;
    } catch (error) {
      this.log("ERROR", "Resend configuration validation failed", {
        error: error.message,
      });
      return false;
    }
  }

  /**
   * Obtém estatísticas específicas do Resend
   * @returns {Object} Estatísticas estendidas
   */
  getStats() {
    const baseStats = super.getStats();

    return {
      ...baseStats,
      domain: this.config.domain,
      apiKeyConfigured: !!this.config.apiKey,
    };
  }

  /**
   * Obtém informações de um email enviado
   * @param {string} emailId - ID do email
   * @returns {Promise<Object>} Informações do email
   */
  async getEmailInfo(emailId) {
    try {
      const email = await this.client.emails.get(emailId);

      return {
        id: email.id,
        status: email.last_event,
        created_at: email.created_at,
        from: email.from,
        to: email.to,
        subject: email.subject,
      };
    } catch (error) {
      this.log("ERROR", "Failed to get email info from Resend", {
        emailId,
        error: error.message,
      });

      return {
        id: emailId,
        status: "unknown",
        error: error.message,
      };
    }
  }

  /**
   * Lista domínios configurados no Resend
   * @returns {Promise<Array>} Lista de domínios
   */
  async listDomains() {
    try {
      const result = await this.client.domains.list();
      return result.data || [];
    } catch (error) {
      this.log("ERROR", "Failed to list Resend domains", {
        error: error.message,
      });
      return [];
    }
  }

  /**
   * Verifica status de entrega de um email
   * @param {string} emailId - ID do email
   * @returns {Promise<Object>} Status de entrega
   */
  async getDeliveryStatus(emailId) {
    try {
      const emailInfo = await this.getEmailInfo(emailId);

      return {
        status: emailInfo.status || "unknown",
        timestamp: emailInfo.created_at,
        reason: emailInfo.error || null,
      };
    } catch (error) {
      this.log("ERROR", "Failed to get delivery status from Resend", {
        emailId,
        error: error.message,
      });

      return {
        status: "unknown",
        timestamp: null,
        reason: error.message,
      };
    }
  }
}

module.exports = ResendProvider;

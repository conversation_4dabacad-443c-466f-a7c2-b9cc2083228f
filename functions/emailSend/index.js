/**
 * EmailSend - Consumidor de Emails do Redis
 * 
 * Este módulo é responsável por buscar emails prontos para envio no Redis
 * e enviá-los através do sistema unificado de provedores, com sistema de retry
 * e dead-letter queue para falhas persistentes.
 * 
 * Segue o padrão arquitetural do shotxSendMessages mas com melhorias específicas para emails.
 */

const { CONSTANTS } = require("../init");
const { momentNow } = require("../helpers");
const { 
  getScheduledMessages, 
  removeMessage, 
  saveScheduledMessage,
  getRedisClient 
} = require("../utils/redisClient");
const { sendEmail } = require("../emailSystem");

const { MOMENT_ISO } = CONSTANTS;

/**
 * Configurações do EmailSend
 */
const EMAIL_SEND_CONFIG = {
  // Número máximo de emails a processar por execução
  batchSize: 50,
  
  // Timeout para envio de email (ms)
  sendTimeout: 30000,
  
  // Configurações de retry
  retry: {
    maxAttempts: 3,
    baseDelay: 60000, // 1 minuto
    maxDelay: 3600000, // 1 hora
    backoffMultiplier: 2,
    jitter: true
  },
  
  // Chaves Redis
  scheduledListKey: 'email:scheduled_emails',
  deadLetterQueueKey: 'email:dead_letter_queue',
  metricsKey: 'email:metrics',
  
  // Prefixo para chaves de email
  redisPrefix: 'email'
};

/**
 * Função principal para processar e enviar emails do Redis
 * @param {Object} options - Opções de processamento
 * @returns {Promise<Object>} Resultado do processamento
 */
const emailSend = async (options = {}) => {
  const startTime = Date.now();
  
  try {
    console.log("EMAIL SEND > Starting email sending process");
    
    // Processar emails agendados
    const result = await processScheduledEmails(options);
    
    const duration = Date.now() - startTime;
    
    console.log(`EMAIL SEND > Completed: ${result.sent} sent, ${result.failed} failed, ${result.retried} retried, ${duration}ms`);
    
    // Atualizar métricas
    await updateMetrics(result);
    
    return {
      success: true,
      ...result,
      duration
    };
    
  } catch (error) {
    const duration = Date.now() - startTime;
    
    console.error("EMAIL SEND > Fatal error:", error.message);
    console.error(error.stack);
    
    return {
      success: false,
      error: error.message,
      sent: 0,
      failed: 0,
      retried: 0,
      duration
    };
  }
};

/**
 * Processa emails agendados do Redis
 * @param {Object} options - Opções de processamento
 * @returns {Promise<Object>} Resultado do processamento
 */
const processScheduledEmails = async (options = {}) => {
  const { 
    batchSize = EMAIL_SEND_CONFIG.batchSize,
    dryRun = false,
    simulateOnly = false 
  } = options;
  
  let sent = 0;
  let failed = 0;
  let retried = 0;
  
  try {
    const now = momentNow().format(MOMENT_ISO);
    const nowTimestamp = new Date(now).getTime();
    
    // Buscar emails prontos para envio
    const emails = await getScheduledMessages(
      EMAIL_SEND_CONFIG.scheduledListKey,
      nowTimestamp,
      "SEND",
      {
        limit: batchSize,
        remove: false
      }
    );
    
    if (emails.length === 0) {
      console.log("EMAIL SEND > No emails ready for sending");
      return { sent, failed, retried };
    }
    
    console.log(`EMAIL SEND > Found ${emails.length} emails ready for sending`);
    
    // Processar emails sequencialmente para controlar carga
    for (const emailData of emails) {
      try {
        if (simulateOnly) {
          console.log("EMAIL SEND > SIMULATION > Would send email:", {
            id: emailData.id,
            to: emailData.to,
            subject: emailData.subject,
            scheduled: emailData._scheduled_iso
          });
          sent++;
          continue;
        }
        
        if (dryRun) {
          console.log("EMAIL SEND > DRY RUN > Email:", emailData.id);
          continue;
        }
        
        // Tentar enviar email
        const result = await sendEmailWithRetry(emailData);
        
        if (result.success) {
          sent++;
          await removeEmailFromQueue(emailData);
        } else if (result.shouldRetry) {
          retried++;
          await scheduleRetry(emailData, result.error);
        } else {
          failed++;
          await moveToDeadLetterQueue(emailData, result.error);
        }
        
      } catch (error) {
        console.error(`EMAIL SEND > Error processing email ${emailData.id}:`, error.message);
        failed++;
        await moveToDeadLetterQueue(emailData, error.message);
      }
    }
    
    return { sent, failed, retried };
    
  } catch (error) {
    console.error("EMAIL SEND > Error processing scheduled emails:", error.message);
    throw error;
  }
};

/**
 * Envia email com sistema de retry
 * @param {Object} emailData - Dados do email
 * @returns {Promise<Object>} Resultado do envio
 */
const sendEmailWithRetry = async (emailData) => {
  const startTime = Date.now();
  
  try {
    console.log(`EMAIL SEND > Attempting to send email ${emailData.id} (attempt ${emailData.attempts + 1})`);
    
    // Atualizar tentativas
    emailData.attempts = (emailData.attempts || 0) + 1;
    emailData.last_attempt = momentNow().format(MOMENT_ISO);
    
    if (emailData.attempts === 1) {
      emailData.first_attempt_at = emailData.last_attempt;
    }
    
    // Enviar email através do sistema unificado
    const result = await sendEmail(emailData);
    
    const duration = Date.now() - startTime;
    
    if (result.success) {
      // Sucesso - atualizar dados do email
      emailData.sent_at = momentNow().format(MOMENT_ISO);
      emailData.trackId = result.trackId;
      
      console.log(`EMAIL SEND > Email ${emailData.id} sent successfully via ${result.provider} (${duration}ms)`);
      
      return {
        success: true,
        provider: result.provider,
        trackId: result.trackId,
        duration
      };
    } else {
      // Falha - determinar se deve tentar novamente
      const shouldRetry = result.temporary && emailData.attempts < EMAIL_SEND_CONFIG.retry.maxAttempts;
      
      console.log(`EMAIL SEND > Email ${emailData.id} failed: ${result.error} (temporary: ${result.temporary}, shouldRetry: ${shouldRetry})`);
      
      return {
        success: false,
        shouldRetry,
        error: result.error,
        temporary: result.temporary,
        duration
      };
    }
    
  } catch (error) {
    const duration = Date.now() - startTime;
    
    console.error(`EMAIL SEND > Error sending email ${emailData.id}:`, error.message);
    
    // Determinar se é erro temporário
    const isTemporary = isTemporaryError(error);
    const shouldRetry = isTemporary && emailData.attempts < EMAIL_SEND_CONFIG.retry.maxAttempts;
    
    return {
      success: false,
      shouldRetry,
      error: error.message,
      temporary: isTemporary,
      duration
    };
  }
};

/**
 * Determina se um erro é temporário
 * @param {Error} error - Erro ocorrido
 * @returns {boolean} True se for erro temporário
 */
const isTemporaryError = (error) => {
  const temporaryMessages = [
    'timeout',
    'rate limit',
    'server error',
    'service unavailable',
    'connection',
    'network',
    'temporary'
  ];
  
  const message = error.message.toLowerCase();
  return temporaryMessages.some(msg => message.includes(msg));
};

/**
 * Agenda retry para um email
 * @param {Object} emailData - Dados do email
 * @param {string} errorMessage - Mensagem de erro
 * @returns {Promise<boolean>} True se reagendado com sucesso
 */
const scheduleRetry = async (emailData, errorMessage) => {
  try {
    // Calcular próximo retry com backoff exponencial
    const nextRetryTime = calculateNextRetry(emailData.attempts);
    
    // Atualizar dados do email
    emailData.next_retry = new Date(nextRetryTime).toISOString();
    emailData.last_error = errorMessage;
    emailData._scheduled_timestamp = nextRetryTime;
    
    // Remover da posição atual
    await removeEmailFromQueue(emailData);
    
    // Reagendar com novo timestamp
    const emailKey = `${EMAIL_SEND_CONFIG.redisPrefix}:message:${emailData.id}`;
    const saved = await saveScheduledMessage(
      EMAIL_SEND_CONFIG.scheduledListKey,
      nextRetryTime,
      emailKey,
      emailData
    );
    
    if (saved) {
      console.log(`EMAIL SEND > Email ${emailData.id} rescheduled for retry at ${emailData.next_retry}`);
      return true;
    }
    
    return false;
    
  } catch (error) {
    console.error(`EMAIL SEND > Error scheduling retry for email ${emailData.id}:`, error.message);
    return false;
  }
};

/**
 * Calcula próximo tempo de retry com backoff exponencial
 * @param {number} attempt - Número da tentativa
 * @returns {number} Timestamp do próximo retry
 */
const calculateNextRetry = (attempt) => {
  const { baseDelay, maxDelay, backoffMultiplier, jitter } = EMAIL_SEND_CONFIG.retry;
  
  // Calcular delay com backoff exponencial
  const delay = Math.min(
    baseDelay * Math.pow(backoffMultiplier, attempt - 1),
    maxDelay
  );
  
  // Adicionar jitter para evitar thundering herd
  const jitterAmount = jitter ? Math.random() * 0.1 * delay : 0;
  
  return Date.now() + delay + jitterAmount;
};

/**
 * Move email para dead letter queue
 * @param {Object} emailData - Dados do email
 * @param {string} errorMessage - Mensagem de erro final
 * @returns {Promise<boolean>} True se movido com sucesso
 */
const moveToDeadLetterQueue = async (emailData, errorMessage) => {
  try {
    // Preparar dados para DLQ
    const dlqData = {
      ...emailData,
      final_error: errorMessage,
      failed_at: momentNow().format(MOMENT_ISO),
      final_attempt: emailData.attempts
    };
    
    // Salvar na dead letter queue
    const dlqKey = `${EMAIL_SEND_CONFIG.redisPrefix}:dlq:${emailData.id}`;
    const saved = await saveScheduledMessage(
      EMAIL_SEND_CONFIG.deadLetterQueueKey,
      Date.now(),
      dlqKey,
      dlqData
    );
    
    if (saved) {
      // Remover da fila principal
      await removeEmailFromQueue(emailData);
      
      console.log(`EMAIL SEND > Email ${emailData.id} moved to dead letter queue: ${errorMessage}`);
      return true;
    }
    
    return false;
    
  } catch (error) {
    console.error(`EMAIL SEND > Error moving email ${emailData.id} to DLQ:`, error.message);
    return false;
  }
};

/**
 * Remove email da fila principal
 * @param {Object} emailData - Dados do email
 * @returns {Promise<boolean>} True se removido com sucesso
 */
const removeEmailFromQueue = async (emailData) => {
  try {
    const emailKey = `${EMAIL_SEND_CONFIG.redisPrefix}:message:${emailData.id}`;
    const removed = await removeMessage(emailKey, EMAIL_SEND_CONFIG.scheduledListKey);
    
    if (removed) {
      console.log(`EMAIL SEND > Email ${emailData.id} removed from queue`);
    }
    
    return removed;
    
  } catch (error) {
    console.error(`EMAIL SEND > Error removing email ${emailData.id} from queue:`, error.message);
    return false;
  }
};

/**
 * Atualiza métricas diárias
 * @param {Object} result - Resultado do processamento
 * @returns {Promise<void>}
 */
const updateMetrics = async (result) => {
  try {
    const redisClient = await getRedisClient();
    if (!redisClient) return;
    
    const today = momentNow().format('YYYY-MM-DD');
    const metricsKey = `${EMAIL_SEND_CONFIG.metricsKey}:${today}`;
    
    // Incrementar contadores
    if (result.sent > 0) {
      await redisClient.hIncrBy(metricsKey, 'sent', result.sent);
    }
    
    if (result.failed > 0) {
      await redisClient.hIncrBy(metricsKey, 'failed', result.failed);
    }
    
    if (result.retried > 0) {
      await redisClient.hIncrBy(metricsKey, 'retried', result.retried);
    }
    
    // Definir expiração de 90 dias
    await redisClient.expire(metricsKey, 90 * 24 * 60 * 60);
    
  } catch (error) {
    console.error("EMAIL SEND > Error updating metrics:", error.message);
  }
};

/**
 * Obtém estatísticas do processo de envio
 * @returns {Promise<Object>} Estatísticas
 */
const getEmailSendStats = async () => {
  try {
    const redisClient = await getRedisClient();
    if (!redisClient) {
      return { error: 'Redis not available' };
    }
    
    // Estatísticas da fila
    const queueSize = await redisClient.zCard(EMAIL_SEND_CONFIG.scheduledListKey);
    const dlqSize = await redisClient.zCard(EMAIL_SEND_CONFIG.deadLetterQueueKey);
    
    // Emails prontos para envio
    const now = Date.now();
    const readyCount = await redisClient.zCount(EMAIL_SEND_CONFIG.scheduledListKey, 0, now);
    
    // Métricas do dia
    const today = momentNow().format('YYYY-MM-DD');
    const metricsKey = `${EMAIL_SEND_CONFIG.metricsKey}:${today}`;
    const dailyMetrics = await redisClient.hGetAll(metricsKey);
    
    return {
      queue: {
        total: queueSize,
        ready: readyCount,
        pending: queueSize - readyCount
      },
      deadLetterQueue: {
        size: dlqSize
      },
      daily: {
        sent: parseInt(dailyMetrics.sent || 0),
        failed: parseInt(dailyMetrics.failed || 0),
        retried: parseInt(dailyMetrics.retried || 0)
      },
      timestamp: momentNow().format(MOMENT_ISO)
    };
    
  } catch (error) {
    console.error("EMAIL SEND > Error getting stats:", error.message);
    return { error: error.message };
  }
};

/**
 * Limpa emails antigos da dead letter queue
 * @param {number} maxAge - Idade máxima em dias (padrão: 30)
 * @returns {Promise<number>} Número de emails removidos
 */
const cleanupDeadLetterQueue = async (maxAge = 30) => {
  try {
    const redisClient = await getRedisClient();
    if (!redisClient) return 0;
    
    const cutoffTime = Date.now() - (maxAge * 24 * 60 * 60 * 1000);
    
    const removed = await redisClient.zRemRangeByScore(
      EMAIL_SEND_CONFIG.deadLetterQueueKey,
      0,
      cutoffTime
    );
    
    console.log(`EMAIL SEND > Cleaned up ${removed} old emails from DLQ`);
    return removed;
    
  } catch (error) {
    console.error("EMAIL SEND > Error cleaning up DLQ:", error.message);
    return 0;
  }
};

module.exports = {
  emailSend,
  processScheduledEmails,
  sendEmailWithRetry,
  getEmailSendStats,
  cleanupDeadLetterQueue,
  EMAIL_SEND_CONFIG
};
